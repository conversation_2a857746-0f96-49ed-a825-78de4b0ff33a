# 文字生成图片 AI - TensorFlow.js

一个基于TensorFlow.js的现代化文字生成图片AI应用，支持多种API提供商和本地图像处理功能。

## ✨ 特性

- 🎨 **多API支持**: 支持OpenAI DALL-E、Stability AI等主流API
- 🧠 **TensorFlow.js集成**: 本地图像分析、滤镜和处理功能
- 🎯 **演示模式**: 无需API密钥即可体验功能
- 📱 **响应式设计**: 完美适配桌面和移动设备
- ⚡ **高性能**: WebGL加速的图像处理
- 🔒 **隐私保护**: API密钥本地存储，不上传服务器
- 🎛️ **丰富配置**: 支持多种图片尺寸、风格和质量选项

## 🚀 快速开始

### 环境要求

- 现代浏览器（支持ES6、WebGL、Canvas）
- Node.js 14+ （用于开发服务器）
- 网络连接（用于API调用和CDN资源）

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd text-to-image-ai
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm start
   ```

4. **打开浏览器**
   访问 `http://localhost:8080`

## 📖 使用指南

### 基础使用

1. **选择API提供商**
   - OpenAI DALL-E: 高质量图像生成
   - Stability AI: 开源Stable Diffusion模型
   - 演示模式: 无需API密钥的体验模式

2. **输入文本描述**
   ```
   一只可爱的橙色小猫坐在彩虹桥上，背景是星空，卡通风格
   ```

3. **配置参数**
   - 图片尺寸: 512×512, 1024×1024等
   - 图片风格: 自然、生动、艺术等
   - 图片质量: 标准、高清

4. **生成图片**
   点击"生成图片"按钮，等待AI创作

5. **分析和下载**
   - 使用TensorFlow.js分析图像内容
   - 下载生成的图片

### API配置

#### OpenAI DALL-E

1. 获取API密钥：访问 [OpenAI Platform](https://platform.openai.com/api-keys)
2. 在应用中选择"OpenAI DALL-E"
3. 输入API密钥（仅本地存储）

支持的参数：
- 模型: DALL-E 3
- 尺寸: 1024×1024, 1024×1792, 1792×1024
- 风格: natural, vivid
- 质量: standard, hd

#### Stability AI

1. 获取API密钥：访问 [Stability AI Platform](https://platform.stability.ai/)
2. 在应用中选择"Stability AI"
3. 输入API密钥

支持的参数：
- 模型: Stable Diffusion XL
- 尺寸: 512×512, 1024×1024
- 风格: enhance, anime, photographic等

## 🛠️ 技术架构

### 前端技术栈

- **TensorFlow.js**: 机器学习和图像处理
- **Vanilla JavaScript**: 核心逻辑实现
- **CSS3**: 现代化样式和动画
- **HTML5**: 语义化标记

### 核心模块

```
src/
├── js/
│   ├── config.js          # 配置管理
│   ├── api.js             # API集成
│   ├── imageProcessor.js  # 图像处理
│   ├── ui.js              # UI控制
│   └── app.js             # 主应用
├── css/
│   ├── style.css          # 主样式
│   └── components.css     # 组件样式
└── assets/                # 静态资源
```

### 架构特点

- **模块化设计**: 清晰的职责分离
- **事件驱动**: 响应式用户交互
- **错误处理**: 完善的异常处理机制
- **性能优化**: 内存管理和资源清理
- **可扩展性**: 易于添加新的API提供商

## 🔧 开发指南

### 添加新的API提供商

1. **在config.js中添加配置**
   ```javascript
   apis: {
     newProvider: {
       name: 'New Provider',
       baseUrl: 'https://api.newprovider.com',
       endpoint: '/generate',
       supportedSizes: ['512x512', '1024x1024']
     }
   }
   ```

2. **在api.js中实现生成方法**
   ```javascript
   async generateWithNewProvider(params) {
     // 实现API调用逻辑
   }
   ```

3. **更新UI选项**
   在HTML中添加对应的选项

### 自定义图像处理

```javascript
// 添加新的滤镜
imageProcessor.addFilter('customFilter', (tensor) => {
  // 实现滤镜逻辑
  return processedTensor;
});
```

### 扩展分析功能

```javascript
// 添加新的分析方法
imageProcessor.addAnalysis('customAnalysis', async (img) => {
  // 实现分析逻辑
  return analysisResult;
});
```

## 📊 性能优化

### TensorFlow.js优化

- **后端选择**: 自动选择最佳后端（WebGL > CPU）
- **内存管理**: 及时清理张量资源
- **模型缓存**: 避免重复加载模型

### 网络优化

- **请求缓存**: 缓存API响应
- **图片压缩**: 优化图片传输
- **CDN加速**: 使用CDN加载依赖

### 用户体验优化

- **加载状态**: 清晰的进度指示
- **错误处理**: 友好的错误提示
- **响应式设计**: 适配各种设备

## 🔒 安全考虑

- **API密钥安全**: 仅在客户端存储，不上传服务器
- **输入验证**: 严格验证用户输入
- **CORS处理**: 正确配置跨域请求
- **内容过滤**: 遵循API提供商的内容政策

## 🐛 故障排除

### 常见问题

**Q: TensorFlow.js初始化失败**
A: 检查浏览器是否支持WebGL，尝试刷新页面

**Q: API调用失败**
A: 检查API密钥是否正确，网络连接是否正常

**Q: 图片无法显示**
A: 检查浏览器控制台错误，可能是CORS问题

**Q: 内存使用过高**
A: 刷新页面清理内存，避免频繁生成大尺寸图片

### 调试工具

```javascript
// 查看应用状态
console.log(window.textToImageApp.getAppInfo());

// 查看TensorFlow.js内存使用
console.log(tf.memory());

// 查看当前设置
console.log(ConfigUtils.loadSettings());
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📞 支持

- 📧 邮箱: <EMAIL>
- 💬 讨论: [GitHub Discussions](https://github.com/example/discussions)
- 🐛 问题: [GitHub Issues](https://github.com/example/issues)

## 🙏 致谢

- [TensorFlow.js](https://www.tensorflow.org/js) - 机器学习框架
- [OpenAI](https://openai.com/) - DALL-E API
- [Stability AI](https://stability.ai/) - Stable Diffusion
- 所有开源贡献者

---

**享受AI创作的乐趣！** 🎨✨
