/**
 * UI控制模块
 * 处理用户界面交互和状态管理
 */

class UIController {
    constructor() {
        this.elements = {};
        this.currentSettings = ConfigUtils.loadSettings();
        this.isGenerating = false;
        this.currentImageData = null;
    }

    /**
     * 初始化UI
     */
    initialize() {
        this.bindElements();
        this.bindEvents();
        this.loadSettings();
        this.updateAPIConfigVisibility();
        console.log('UI控制器初始化完成');
    }

    /**
     * 绑定DOM元素
     */
    bindElements() {
        this.elements = {
            // 输入元素
            textPrompt: document.getElementById('textPrompt'),
            apiProvider: document.getElementById('apiProvider'),
            imageSize: document.getElementById('imageSize'),
            imageStyle: document.getElementById('imageStyle'),
            imageQuality: document.getElementById('imageQuality'),
            apiKey: document.getElementById('apiKey'),
            
            // 按钮元素
            generateBtn: document.getElementById('generateBtn'),
            clearBtn: document.getElementById('clearBtn'),
            downloadBtn: document.getElementById('downloadBtn'),
            analyzeBtn: document.getElementById('analyzeBtn'),
            
            // 显示元素
            statusDisplay: document.getElementById('statusDisplay'),
            imageDisplay: document.getElementById('imageDisplay'),
            generatedImage: document.getElementById('generatedImage'),
            imageMetadata: document.getElementById('imageMetadata'),
            analysisResults: document.getElementById('analysisResults'),
            analysisContent: document.getElementById('analysisContent'),
            apiConfig: document.getElementById('apiConfig'),
            
            // 按钮文本
            btnText: document.querySelector('.btn-text'),
            btnLoading: document.querySelector('.btn-loading')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 生成按钮
        this.elements.generateBtn.addEventListener('click', () => this.handleGenerate());
        
        // 清空按钮
        this.elements.clearBtn.addEventListener('click', () => this.handleClear());
        
        // 下载按钮
        this.elements.downloadBtn.addEventListener('click', () => this.handleDownload());
        
        // 分析按钮
        this.elements.analyzeBtn.addEventListener('click', () => this.handleAnalyze());
        
        // API提供商变更
        this.elements.apiProvider.addEventListener('change', () => {
            this.handleProviderChange();
            this.updateAPIConfigVisibility();
        });
        
        // 设置变更
        ['imageSize', 'imageStyle', 'imageQuality'].forEach(id => {
            this.elements[id].addEventListener('change', () => this.saveCurrentSettings());
        });
        
        // API密钥变更
        this.elements.apiKey.addEventListener('input', () => this.handleApiKeyChange());
        
        // 文本输入变更
        this.elements.textPrompt.addEventListener('input', () => this.validateInput());
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }

    /**
     * 加载设置
     */
    loadSettings() {
        this.elements.apiProvider.value = this.currentSettings.apiProvider;
        this.elements.imageSize.value = this.currentSettings.imageSize;
        this.elements.imageStyle.value = this.currentSettings.imageStyle;
        this.elements.imageQuality.value = this.currentSettings.imageQuality;
        this.elements.textPrompt.value = this.currentSettings.prompt;
        
        // 加载API密钥
        const apiKey = ConfigUtils.getApiKey(this.currentSettings.apiProvider);
        if (apiKey) {
            this.elements.apiKey.value = apiKey;
        }
    }

    /**
     * 保存当前设置
     */
    saveCurrentSettings() {
        this.currentSettings = {
            apiProvider: this.elements.apiProvider.value,
            imageSize: this.elements.imageSize.value,
            imageStyle: this.elements.imageStyle.value,
            imageQuality: this.elements.imageQuality.value,
            prompt: this.elements.textPrompt.value
        };
        
        ConfigUtils.saveSettings(this.currentSettings);
    }

    /**
     * 处理生成图片
     */
    async handleGenerate() {
        if (this.isGenerating) return;
        
        try {
            // 验证输入
            if (!this.validateInput()) return;
            
            // 设置生成状态
            this.setGeneratingState(true);
            
            // 准备参数
            const params = {
                prompt: this.elements.textPrompt.value.trim(),
                size: this.elements.imageSize.value,
                style: this.elements.imageStyle.value,
                quality: this.elements.imageQuality.value
            };
            
            // 设置API提供商
            textToImageAPI.setProvider(this.elements.apiProvider.value);
            
            // 更新状态
            this.updateStatus('正在生成图片，请稍候...', 'info');
            
            // 调用API生成图片
            const result = await textToImageAPI.generateImage(params);
            
            // 显示结果
            await this.displayGeneratedImage(result);
            
            // 保存设置
            this.saveCurrentSettings();
            
            this.showNotification(CONFIG.messages.imageGenerated, 'success');
            
        } catch (error) {
            console.error('图片生成失败:', error);
            this.showNotification(error.message, 'error');
            this.updateStatus('图片生成失败，请重试', 'error');
        } finally {
            this.setGeneratingState(false);
        }
    }

    /**
     * 处理清空
     */
    handleClear() {
        this.elements.textPrompt.value = '';
        this.elements.imageDisplay.style.display = 'none';
        this.elements.analysisResults.style.display = 'none';
        this.updateStatus('请输入文本描述并点击生成按钮', 'info');
        this.currentImageData = null;
    }

    /**
     * 处理下载
     */
    async handleDownload() {
        if (!this.currentImageData) {
            this.showNotification('没有可下载的图片', 'warning');
            return;
        }
        
        try {
            const link = document.createElement('a');
            link.href = this.currentImageData.imageUrl;
            link.download = `generated-image-${Date.now()}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            this.showNotification(CONFIG.messages.imageDownloaded, 'success');
        } catch (error) {
            console.error('下载失败:', error);
            this.showNotification('下载失败，请重试', 'error');
        }
    }

    /**
     * 处理图像分析
     */
    async handleAnalyze() {
        if (!this.currentImageData) {
            this.showNotification('没有可分析的图片', 'warning');
            return;
        }
        
        try {
            this.updateStatus('正在分析图像...', 'info');
            
            // 使用TensorFlow.js分析图像
            const analysisResult = await imageProcessor.analyzeImage(this.elements.generatedImage);
            
            // 显示分析结果
            this.displayAnalysisResults(analysisResult);
            
            this.showNotification(CONFIG.messages.analysisComplete, 'success');
            
        } catch (error) {
            console.error('图像分析失败:', error);
            this.showNotification('图像分析失败，请重试', 'error');
        }
    }

    /**
     * 处理API提供商变更
     */
    handleProviderChange() {
        const provider = this.elements.apiProvider.value;
        
        // 更新支持的选项
        this.updateSupportedOptions(provider);
        
        // 加载对应的API密钥
        const apiKey = ConfigUtils.getApiKey(provider);
        this.elements.apiKey.value = apiKey || '';
        
        // 验证输入
        this.validateInput();
    }

    /**
     * 更新支持的选项
     * @param {string} provider - API提供商
     */
    updateSupportedOptions(provider) {
        const options = textToImageAPI.getSupportedOptions(provider);
        
        // 更新尺寸选项
        this.updateSelectOptions(this.elements.imageSize, options.sizes);
        
        // 更新风格选项
        this.updateSelectOptions(this.elements.imageStyle, options.styles);
        
        // 更新质量选项
        this.updateSelectOptions(this.elements.imageQuality, options.qualities);
    }

    /**
     * 更新选择框选项
     * @param {HTMLSelectElement} selectElement - 选择框元素
     * @param {Array} options - 选项数组
     */
    updateSelectOptions(selectElement, options) {
        const currentValue = selectElement.value;
        selectElement.innerHTML = '';
        
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option;
            selectElement.appendChild(optionElement);
        });
        
        // 尝试保持当前选择
        if (options.includes(currentValue)) {
            selectElement.value = currentValue;
        }
    }

    /**
     * 处理API密钥变更
     */
    handleApiKeyChange() {
        const provider = this.elements.apiProvider.value;
        const apiKey = this.elements.apiKey.value.trim();
        
        if (apiKey) {
            ConfigUtils.saveApiKey(provider, apiKey);
        }
        
        this.validateInput();
    }

    /**
     * 验证输入
     * @returns {boolean} 是否有效
     */
    validateInput() {
        const prompt = this.elements.textPrompt.value.trim();
        const provider = this.elements.apiProvider.value;
        const hasApiKey = textToImageAPI.hasApiKey(provider);
        
        let isValid = true;
        let message = '';
        
        if (!prompt) {
            isValid = false;
            message = '请输入图片描述';
        } else if (!hasApiKey && provider !== 'demo') {
            isValid = false;
            message = '请输入API密钥';
        }
        
        // 更新按钮状态
        this.elements.generateBtn.disabled = !isValid || this.isGenerating;
        
        if (!isValid && message) {
            this.updateStatus(message, 'warning');
        } else if (isValid && !this.isGenerating) {
            this.updateStatus('准备就绪，点击生成按钮开始', 'info');
        }
        
        return isValid;
    }

    /**
     * 设置生成状态
     * @param {boolean} generating - 是否正在生成
     */
    setGeneratingState(generating) {
        this.isGenerating = generating;
        
        if (generating) {
            this.elements.btnText.style.display = 'none';
            this.elements.btnLoading.style.display = 'flex';
            this.elements.generateBtn.disabled = true;
        } else {
            this.elements.btnText.style.display = 'flex';
            this.elements.btnLoading.style.display = 'none';
            this.validateInput(); // 重新验证以更新按钮状态
        }
    }

    /**
     * 更新状态显示
     * @param {string} message - 状态消息
     * @param {string} type - 消息类型
     */
    updateStatus(message, type = 'info') {
        this.elements.statusDisplay.textContent = message;
        this.elements.statusDisplay.className = `status-display ${type}`;
    }

    /**
     * 显示生成的图片
     * @param {object} result - 生成结果
     */
    async displayGeneratedImage(result) {
        this.currentImageData = result;
        
        // 设置图片
        this.elements.generatedImage.src = result.imageUrl;
        this.elements.generatedImage.alt = result.revisedPrompt || '生成的图片';
        
        // 显示图片容器
        this.elements.imageDisplay.style.display = 'block';
        this.elements.imageDisplay.classList.add('fade-in');
        
        // 显示元数据
        this.displayImageMetadata(result.metadata);
        
        // 更新状态
        this.updateStatus('图片生成完成！您可以下载或分析图片', 'success');
    }

    /**
     * 显示图片元数据
     * @param {object} metadata - 元数据
     */
    displayImageMetadata(metadata) {
        const metadataHtml = Object.entries(metadata)
            .map(([key, value]) => `
                <div class="metadata-item">
                    <span class="metadata-label">${this.formatMetadataKey(key)}:</span>
                    <span class="metadata-value">${value}</span>
                </div>
            `).join('');
        
        this.elements.imageMetadata.innerHTML = metadataHtml;
    }

    /**
     * 格式化元数据键名
     * @param {string} key - 键名
     * @returns {string} 格式化后的键名
     */
    formatMetadataKey(key) {
        const keyMap = {
            provider: 'API提供商',
            model: '模型',
            size: '尺寸',
            style: '风格',
            quality: '质量',
            timestamp: '生成时间'
        };
        
        return keyMap[key] || key;
    }

    /**
     * 显示分析结果
     * @param {object} analysisResult - 分析结果
     */
    displayAnalysisResults(analysisResult) {
        let html = '';
        
        // 基本信息
        if (analysisResult.basicInfo) {
            html += this.createAnalysisSection('基本信息', analysisResult.basicInfo);
        }
        
        // 颜色分析
        if (analysisResult.colorAnalysis) {
            html += this.createColorAnalysisSection(analysisResult.colorAnalysis);
        }
        
        // 分类结果
        if (analysisResult.classification) {
            html += this.createClassificationSection(analysisResult.classification);
        }
        
        // 统计信息
        if (analysisResult.statistics) {
            html += this.createAnalysisSection('统计信息', analysisResult.statistics);
        }
        
        this.elements.analysisContent.innerHTML = html;
        this.elements.analysisResults.style.display = 'block';
        this.elements.analysisResults.classList.add('fade-in');
    }

    /**
     * 创建分析部分
     * @param {string} title - 标题
     * @param {object} data - 数据
     * @returns {string} HTML字符串
     */
    createAnalysisSection(title, data) {
        const items = Object.entries(data)
            .map(([key, value]) => `
                <div class="analysis-item">
                    <strong>${this.formatAnalysisKey(key)}:</strong> ${this.formatAnalysisValue(value)}
                </div>
            `).join('');
        
        return `
            <div class="analysis-section">
                <h4>${title}</h4>
                ${items}
            </div>
        `;
    }

    /**
     * 创建颜色分析部分
     * @param {object} colorData - 颜色数据
     * @returns {string} HTML字符串
     */
    createColorAnalysisSection(colorData) {
        return `
            <div class="analysis-section">
                <h4>颜色分析</h4>
                <div class="analysis-item">
                    <strong>平均颜色:</strong> 
                    <span style="display: inline-block; width: 20px; height: 20px; background-color: ${colorData.averageColor.hex}; border: 1px solid #ccc; margin-left: 5px;"></span>
                    ${colorData.averageColor.hex}
                </div>
                <div class="analysis-item">
                    <strong>主导颜色通道:</strong> ${colorData.dominantChannel}
                </div>
                <div class="analysis-item">
                    <strong>亮度:</strong> ${colorData.brightness}/255
                </div>
            </div>
        `;
    }

    /**
     * 创建分类部分
     * @param {Array} classifications - 分类结果
     * @returns {string} HTML字符串
     */
    createClassificationSection(classifications) {
        const items = classifications.map(item => `
            <div class="analysis-item">
                <strong>${item.label}</strong> (${item.confidence}%)
                ${item.description ? `<br><small>${item.description}</small>` : ''}
            </div>
        `).join('');
        
        return `
            <div class="analysis-section">
                <h4>内容分类</h4>
                ${items}
            </div>
        `;
    }

    /**
     * 格式化分析键名
     * @param {string} key - 键名
     * @returns {string} 格式化后的键名
     */
    formatAnalysisKey(key) {
        const keyMap = {
            width: '宽度',
            height: '高度',
            aspectRatio: '宽高比',
            size: '尺寸',
            type: '类型',
            meanIntensity: '平均强度',
            standardDeviation: '标准差',
            minValue: '最小值',
            maxValue: '最大值',
            pixelCount: '像素数量',
            channels: '颜色通道'
        };
        
        return keyMap[key] || key;
    }

    /**
     * 格式化分析值
     * @param {any} value - 值
     * @returns {string} 格式化后的值
     */
    formatAnalysisValue(value) {
        if (typeof value === 'object') {
            return JSON.stringify(value);
        }
        return String(value);
    }

    /**
     * 更新API配置可见性
     */
    updateAPIConfigVisibility() {
        const provider = this.elements.apiProvider.value;
        if (provider === 'demo') {
            this.elements.apiConfig.style.display = 'none';
        } else {
            this.elements.apiConfig.style.display = 'block';
        }
    }

    /**
     * 处理键盘事件
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyboard(e) {
        // Ctrl/Cmd + Enter 生成图片
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            if (!this.isGenerating) {
                this.handleGenerate();
            }
        }
        
        // Escape 取消生成
        if (e.key === 'Escape' && this.isGenerating) {
            textToImageAPI.cancelRequest();
            this.setGeneratingState(false);
            this.updateStatus('生成已取消', 'warning');
        }
    }

    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);
        
        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, CONFIG.ui.notifications.duration);
    }
}

// 创建全局UI控制器实例
const uiController = new UIController();
