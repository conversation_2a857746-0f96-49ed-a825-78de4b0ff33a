/**
 * API集成模块
 * 处理各种文字生成图片API的调用
 */

class TextToImageAPI {
    constructor() {
        this.currentProvider = CONFIG.defaults.apiProvider;
        this.requestController = null; // 用于取消请求
    }

    /**
     * 设置API提供商
     * @param {string} provider - API提供商名称
     */
    setProvider(provider) {
        this.currentProvider = provider;
    }

    /**
     * 生成图片
     * @param {object} params - 生成参数
     * @returns {Promise<object>} 生成结果
     */
    async generateImage(params) {
        const { prompt, size, style, quality } = params;
        
        // 验证参数
        this.validateParams(params);

        // 取消之前的请求
        if (this.requestController) {
            this.requestController.abort();
        }
        this.requestController = new AbortController();

        try {
            switch (this.currentProvider) {
                case 'openai':
                    return await this.generateWithOpenAI(params);
                case 'stability':
                    return await this.generateWithStability(params);
                case 'demo':
                    return await this.generateWithDemo(params);
                default:
                    throw new Error(`不支持的API提供商: ${this.currentProvider}`);
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('请求已取消');
            }
            throw error;
        }
    }

    /**
     * 使用OpenAI DALL-E API生成图片
     * @param {object} params - 生成参数
     * @returns {Promise<object>} 生成结果
     */
    async generateWithOpenAI(params) {
        const { prompt, size, style, quality } = params;
        const apiKey = ConfigUtils.getApiKey('openai');
        
        if (!apiKey) {
            throw new Error(ConfigUtils.getErrorMessage('apiKeyMissing'));
        }

        const config = ConfigUtils.getApiConfig('openai');
        const requestBody = {
            model: config.model,
            prompt: prompt,
            n: 1,
            size: size,
            quality: quality,
            style: style,
            response_format: 'url'
        };

        const response = await fetch(`${config.baseUrl}${config.endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify(requestBody),
            signal: this.requestController.signal
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(this.handleAPIError(response.status, errorData));
        }

        const data = await response.json();
        
        if (!data.data || !data.data[0] || !data.data[0].url) {
            throw new Error(ConfigUtils.getErrorMessage('invalidResponse'));
        }

        return {
            imageUrl: data.data[0].url,
            revisedPrompt: data.data[0].revised_prompt || prompt,
            metadata: {
                provider: 'openai',
                model: config.model,
                size: size,
                style: style,
                quality: quality,
                timestamp: new Date().toISOString()
            }
        };
    }

    /**
     * 使用Stability AI API生成图片
     * @param {object} params - 生成参数
     * @returns {Promise<object>} 生成结果
     */
    async generateWithStability(params) {
        const { prompt, size, style } = params;
        const apiKey = ConfigUtils.getApiKey('stability');
        
        if (!apiKey) {
            throw new Error(ConfigUtils.getErrorMessage('apiKeyMissing'));
        }

        const config = ConfigUtils.getApiConfig('stability');
        const [width, height] = size.split('x').map(Number);
        
        const formData = new FormData();
        formData.append('text_prompts[0][text]', prompt);
        formData.append('text_prompts[0][weight]', '1');
        formData.append('cfg_scale', '7');
        formData.append('width', width.toString());
        formData.append('height', height.toString());
        formData.append('steps', '30');
        formData.append('samples', '1');
        if (style && style !== 'natural') {
            formData.append('style_preset', style);
        }

        const response = await fetch(`${config.baseUrl}${config.endpoint}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Accept': 'application/json'
            },
            body: formData,
            signal: this.requestController.signal
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(this.handleAPIError(response.status, errorData));
        }

        const data = await response.json();
        
        if (!data.artifacts || !data.artifacts[0] || !data.artifacts[0].base64) {
            throw new Error(ConfigUtils.getErrorMessage('invalidResponse'));
        }

        // 将base64转换为blob URL
        const base64Data = data.artifacts[0].base64;
        const imageUrl = `data:image/png;base64,${base64Data}`;

        return {
            imageUrl: imageUrl,
            revisedPrompt: prompt,
            metadata: {
                provider: 'stability',
                size: size,
                style: style,
                steps: 30,
                cfg_scale: 7,
                timestamp: new Date().toISOString()
            }
        };
    }

    /**
     * 演示模式生成图片
     * @param {object} params - 生成参数
     * @returns {Promise<object>} 生成结果
     */
    async generateWithDemo(params) {
        const { prompt } = params;
        
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

        // 根据提示词选择最匹配的演示图片
        const demoImage = this.selectDemoImage(prompt);
        
        return {
            imageUrl: demoImage.url,
            revisedPrompt: demoImage.prompt,
            metadata: {
                provider: 'demo',
                originalPrompt: prompt,
                ...demoImage.metadata,
                timestamp: new Date().toISOString()
            }
        };
    }

    /**
     * 根据提示词选择演示图片
     * @param {string} prompt - 用户输入的提示词
     * @returns {object} 选中的演示图片
     */
    selectDemoImage(prompt) {
        const keywords = {
            '猫|cat|小猫|宠物': 0,
            '城市|city|未来|科技|霓虹|赛博': 1,
            '山|湖|自然|风景|landscape': 2,
            '艺术|抽象|art|几何': 3
        };

        const lowerPrompt = prompt.toLowerCase();
        
        for (const [pattern, index] of Object.entries(keywords)) {
            const regex = new RegExp(pattern, 'i');
            if (regex.test(lowerPrompt)) {
                return CONFIG.demoImages[index];
            }
        }

        // 默认返回第一张图片
        return CONFIG.demoImages[0];
    }

    /**
     * 验证生成参数
     * @param {object} params - 参数对象
     */
    validateParams(params) {
        const { prompt, size, style } = params;
        
        if (!prompt || prompt.trim().length === 0) {
            throw new Error(ConfigUtils.getErrorMessage('promptEmpty'));
        }

        const config = ConfigUtils.getApiConfig(this.currentProvider);
        
        if (config.maxPromptLength && prompt.length > config.maxPromptLength) {
            throw new Error(ConfigUtils.getErrorMessage('promptTooLong', { 
                maxLength: config.maxPromptLength 
            }));
        }

        if (!ConfigUtils.isSizeSupported(this.currentProvider, size)) {
            throw new Error(`当前API不支持尺寸: ${size}`);
        }

        if (!ConfigUtils.isStyleSupported(this.currentProvider, style)) {
            throw new Error(`当前API不支持风格: ${style}`);
        }
    }

    /**
     * 处理API错误
     * @param {number} status - HTTP状态码
     * @param {object} errorData - 错误数据
     * @returns {string} 错误消息
     */
    handleAPIError(status, errorData) {
        switch (status) {
            case 400:
                return `请求参数错误: ${errorData.error?.message || '未知错误'}`;
            case 401:
                return 'API密钥无效或已过期';
            case 402:
                return 'API配额不足，请检查账户余额';
            case 403:
                return 'API访问被拒绝，请检查权限设置';
            case 429:
                return 'API调用频率过高，请稍后重试';
            case 500:
                return 'API服务器内部错误，请稍后重试';
            case 503:
                return 'API服务暂时不可用，请稍后重试';
            default:
                return `API调用失败 (${status}): ${errorData.error?.message || '未知错误'}`;
        }
    }

    /**
     * 取消当前请求
     */
    cancelRequest() {
        if (this.requestController) {
            this.requestController.abort();
            this.requestController = null;
        }
    }

    /**
     * 检查API密钥是否已设置
     * @param {string} provider - API提供商
     * @returns {boolean} 是否已设置
     */
    hasApiKey(provider) {
        if (provider === 'demo') return true;
        return !!ConfigUtils.getApiKey(provider);
    }

    /**
     * 获取支持的参数选项
     * @param {string} provider - API提供商
     * @returns {object} 支持的选项
     */
    getSupportedOptions(provider) {
        const config = ConfigUtils.getApiConfig(provider);
        return {
            sizes: config.supportedSizes || ['512x512', '1024x1024'],
            styles: config.supportedStyles || ['natural', 'vivid'],
            qualities: config.supportedQualities || ['standard', 'hd']
        };
    }
}

// 创建全局API实例
const textToImageAPI = new TextToImageAPI();
