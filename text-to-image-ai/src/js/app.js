/**
 * 主应用文件
 * 应用程序入口点和初始化逻辑
 */

class TextToImageApp {
    constructor() {
        this.isInitialized = false;
        this.version = '1.0.0';
    }

    /**
     * 初始化应用程序
     */
    async initialize() {
        if (this.isInitialized) return;

        try {
            console.log(`🎨 文字生成图片 AI v${this.version} 正在启动...`);
            
            // 显示加载状态
            this.showLoadingState();
            
            // 检查浏览器兼容性
            this.checkBrowserCompatibility();
            
            // 初始化TensorFlow.js
            await this.initializeTensorFlow();
            
            // 初始化UI控制器
            this.initializeUI();
            
            // 设置错误处理
            this.setupErrorHandling();
            
            // 设置性能监控
            this.setupPerformanceMonitoring();
            
            // 隐藏加载状态
            this.hideLoadingState();
            
            this.isInitialized = true;
            console.log('✅ 应用程序初始化完成');
            
            // 显示欢迎消息
            this.showWelcomeMessage();
            
        } catch (error) {
            console.error('❌ 应用程序初始化失败:', error);
            this.showInitializationError(error);
        }
    }

    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility() {
        const requirements = {
            webgl: this.checkWebGLSupport(),
            es6: this.checkES6Support(),
            fetch: typeof fetch !== 'undefined',
            canvas: this.checkCanvasSupport(),
            localStorage: this.checkLocalStorageSupport()
        };

        const unsupported = Object.entries(requirements)
            .filter(([feature, supported]) => !supported)
            .map(([feature]) => feature);

        if (unsupported.length > 0) {
            throw new Error(`浏览器不支持以下功能: ${unsupported.join(', ')}`);
        }

        console.log('✅ 浏览器兼容性检查通过');
    }

    /**
     * 检查WebGL支持
     * @returns {boolean} 是否支持WebGL
     */
    checkWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            return !!gl;
        } catch (e) {
            return false;
        }
    }

    /**
     * 检查ES6支持
     * @returns {boolean} 是否支持ES6
     */
    checkES6Support() {
        try {
            eval('class TestClass {}; const arrow = () => {}; let test = `template`;');
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 检查Canvas支持
     * @returns {boolean} 是否支持Canvas
     */
    checkCanvasSupport() {
        try {
            const canvas = document.createElement('canvas');
            return !!(canvas.getContext && canvas.getContext('2d'));
        } catch (e) {
            return false;
        }
    }

    /**
     * 检查LocalStorage支持
     * @returns {boolean} 是否支持LocalStorage
     */
    checkLocalStorageSupport() {
        try {
            const test = 'test';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 初始化TensorFlow.js
     */
    async initializeTensorFlow() {
        try {
            console.log('🧠 正在初始化 TensorFlow.js...');
            await imageProcessor.initialize();
            console.log('✅ TensorFlow.js 初始化完成');
        } catch (error) {
            console.warn('⚠️ TensorFlow.js 初始化失败，图像分析功能将受限:', error);
            // 不抛出错误，允许应用继续运行
        }
    }

    /**
     * 初始化UI控制器
     */
    initializeUI() {
        console.log('🎨 正在初始化用户界面...');
        uiController.initialize();
        console.log('✅ 用户界面初始化完成');
    }

    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', (event) => {
            console.error('全局错误:', event.error);
            this.handleGlobalError(event.error);
        });

        // Promise 拒绝处理
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的 Promise 拒绝:', event.reason);
            this.handleGlobalError(event.reason);
        });

        console.log('✅ 错误处理设置完成');
    }

    /**
     * 设置性能监控
     */
    setupPerformanceMonitoring() {
        // 监控内存使用
        if (performance.memory) {
            setInterval(() => {
                const memory = performance.memory;
                const memoryUsage = {
                    used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
                };
                
                // 如果内存使用超过80%，发出警告
                if (memoryUsage.used / memoryUsage.limit > 0.8) {
                    console.warn('⚠️ 内存使用率较高:', memoryUsage);
                }
            }, 30000); // 每30秒检查一次
        }

        // 监控TensorFlow.js内存
        if (typeof tf !== 'undefined') {
            setInterval(() => {
                const numTensors = tf.memory().numTensors;
                if (numTensors > 100) {
                    console.warn('⚠️ TensorFlow.js 张量数量较多:', numTensors);
                }
            }, 10000); // 每10秒检查一次
        }

        console.log('✅ 性能监控设置完成');
    }

    /**
     * 处理全局错误
     * @param {Error} error - 错误对象
     */
    handleGlobalError(error) {
        // 避免重复处理相同错误
        if (this.lastError && this.lastError.message === error.message) {
            return;
        }
        this.lastError = error;

        // 显示用户友好的错误消息
        let userMessage = '应用程序遇到了一个错误';
        
        if (error.message.includes('network') || error.message.includes('fetch')) {
            userMessage = '网络连接错误，请检查网络设置';
        } else if (error.message.includes('API')) {
            userMessage = 'API调用失败，请检查API配置';
        } else if (error.message.includes('TensorFlow')) {
            userMessage = '图像处理错误，请刷新页面重试';
        }

        // 显示错误通知
        if (typeof uiController !== 'undefined' && uiController.showNotification) {
            uiController.showNotification(userMessage, 'error');
        }
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'loading-overlay';
        loadingOverlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <h3>正在初始化应用程序...</h3>
                <p>请稍候，正在加载必要的组件</p>
            </div>
        `;
        
        // 添加样式
        loadingOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        document.body.appendChild(loadingOverlay);
    }

    /**
     * 隐藏加载状态
     */
    hideLoadingState() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.opacity = '0';
            setTimeout(() => {
                if (loadingOverlay.parentNode) {
                    loadingOverlay.parentNode.removeChild(loadingOverlay);
                }
            }, 300);
        }
    }

    /**
     * 显示初始化错误
     * @param {Error} error - 错误对象
     */
    showInitializationError(error) {
        this.hideLoadingState();
        
        const errorOverlay = document.createElement('div');
        errorOverlay.innerHTML = `
            <div class="error-content">
                <h2>❌ 应用程序初始化失败</h2>
                <p>抱歉，应用程序无法正常启动。</p>
                <details>
                    <summary>错误详情</summary>
                    <pre>${error.message}</pre>
                </details>
                <button onclick="location.reload()">重新加载</button>
            </div>
        `;
        
        errorOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        document.body.appendChild(errorOverlay);
    }

    /**
     * 显示欢迎消息
     */
    showWelcomeMessage() {
        if (typeof uiController !== 'undefined' && uiController.showNotification) {
            uiController.showNotification('欢迎使用文字生成图片 AI！', 'success');
        }
    }

    /**
     * 获取应用信息
     * @returns {object} 应用信息
     */
    getAppInfo() {
        return {
            version: this.version,
            isInitialized: this.isInitialized,
            tensorflowVersion: typeof tf !== 'undefined' ? tf.version.tfjs : 'Not loaded',
            backend: typeof tf !== 'undefined' ? tf.getBackend() : 'Not available',
            memoryInfo: typeof tf !== 'undefined' ? tf.memory() : null,
            browserInfo: {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                webgl: this.checkWebGLSupport(),
                localStorage: this.checkLocalStorageSupport()
            }
        };
    }

    /**
     * 清理资源
     */
    cleanup() {
        console.log('🧹 正在清理应用程序资源...');
        
        // 清理TensorFlow.js资源
        if (typeof imageProcessor !== 'undefined') {
            imageProcessor.dispose();
        }
        
        // 取消正在进行的API请求
        if (typeof textToImageAPI !== 'undefined') {
            textToImageAPI.cancelRequest();
        }
        
        console.log('✅ 资源清理完成');
    }
}

// 创建全局应用实例
const app = new TextToImageApp();

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    app.initialize();
});

// 页面卸载前清理资源
window.addEventListener('beforeunload', () => {
    app.cleanup();
});

// 导出应用实例（用于调试）
window.textToImageApp = app;
