# API配置指南

本指南将帮助您配置各种文字生成图片API，以便在应用中使用。

## 🔑 API密钥获取

### OpenAI DALL-E

1. **注册OpenAI账户**
   - 访问 [OpenAI官网](https://openai.com/)
   - 点击"Sign up"创建账户
   - 验证邮箱和手机号

2. **获取API密钥**
   - 登录后访问 [API Keys页面](https://platform.openai.com/api-keys)
   - 点击"Create new secret key"
   - 复制生成的密钥（以`sk-`开头）
   - **重要**: 密钥只显示一次，请妥善保存

3. **充值账户**
   - 访问 [Billing页面](https://platform.openai.com/account/billing)
   - 添加付款方式
   - 充值至少$5（DALL-E 3的最低要求）

4. **定价信息**
   - DALL-E 3 (1024×1024): $0.040/张
   - DALL-E 3 (1024×1792, 1792×1024): $0.080/张

### Stability AI

1. **注册Stability AI账户**
   - 访问 [Stability AI Platform](https://platform.stability.ai/)
   - 使用Google或GitHub账户登录
   - 完成账户验证

2. **获取API密钥**
   - 进入 [API Keys页面](https://platform.stability.ai/account/keys)
   - 点击"Create API Key"
   - 输入密钥名称和描述
   - 复制生成的密钥（以`sk-`开头）

3. **获取免费积分**
   - 新用户可获得25个免费积分
   - 每个积分可生成1张图片

4. **定价信息**
   - Stable Diffusion XL: 6.5积分/张
   - 积分包: $10/1000积分

## ⚙️ 应用中的配置

### 配置OpenAI DALL-E

1. **在应用中选择API提供商**
   ```
   API提供商: OpenAI DALL-E
   ```

2. **输入API密钥**
   ```
   API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   ```

3. **支持的参数**
   - **图片尺寸**: 
     - 1024×1024 (正方形)
     - 1024×1792 (竖版)
     - 1792×1024 (横版)
   
   - **图片风格**:
     - natural: 自然风格
     - vivid: 生动风格
   
   - **图片质量**:
     - standard: 标准质量
     - hd: 高清质量

### 配置Stability AI

1. **在应用中选择API提供商**
   ```
   API提供商: Stability AI
   ```

2. **输入API密钥**
   ```
   API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   ```

3. **支持的参数**
   - **图片尺寸**:
     - 512×512
     - 1024×1024
   
   - **图片风格**:
     - enhance: 增强
     - anime: 动漫
     - photographic: 摄影
     - digital-art: 数字艺术
     - comic-book: 漫画书
     - fantasy-art: 奇幻艺术
     - line-art: 线条艺术
     - analog-film: 胶片
     - neon-punk: 霓虹朋克
     - isometric: 等距视角

## 🛡️ 安全最佳实践

### API密钥安全

1. **本地存储**
   - API密钥仅在浏览器本地存储
   - 不会上传到任何服务器
   - 使用localStorage安全存储

2. **密钥保护**
   - 不要在公共场所输入API密钥
   - 定期轮换API密钥
   - 监控API使用情况

3. **权限控制**
   - 为API密钥设置适当的权限
   - 限制API调用频率
   - 设置使用配额

### 使用限制

1. **OpenAI限制**
   - 每分钟最多50次请求
   - 每天最多500次请求（免费用户）
   - 内容必须符合使用政策

2. **Stability AI限制**
   - 每分钟最多150次请求
   - 每月免费积分有限
   - 遵守内容政策

## 🔧 故障排除

### 常见错误

**401 Unauthorized**
```
错误: API密钥无效或已过期
解决: 检查API密钥是否正确输入
```

**402 Payment Required**
```
错误: 账户余额不足
解决: 充值账户或检查计费设置
```

**429 Too Many Requests**
```
错误: API调用频率过高
解决: 等待一段时间后重试
```

**400 Bad Request**
```
错误: 请求参数错误
解决: 检查文本描述是否符合要求
```

### 调试技巧

1. **检查网络连接**
   ```javascript
   // 在浏览器控制台中测试
   fetch('https://api.openai.com/v1/models', {
     headers: { 'Authorization': 'Bearer YOUR_API_KEY' }
   }).then(r => console.log(r.status));
   ```

2. **验证API密钥格式**
   ```javascript
   // OpenAI密钥格式
   const isValidOpenAI = apiKey.startsWith('sk-') && apiKey.length > 40;
   
   // Stability AI密钥格式
   const isValidStability = apiKey.startsWith('sk-') && apiKey.length > 40;
   ```

3. **查看详细错误**
   ```javascript
   // 打开浏览器开发者工具
   // 查看Network标签页的API请求
   // 检查Response中的错误详情
   ```

## 📊 使用监控

### 跟踪API使用

1. **OpenAI使用情况**
   - 访问 [Usage页面](https://platform.openai.com/account/usage)
   - 查看每日/每月使用统计
   - 监控费用支出

2. **Stability AI使用情况**
   - 访问 [Account页面](https://platform.stability.ai/account)
   - 查看积分余额
   - 监控API调用次数

### 成本优化

1. **选择合适的参数**
   - 标准质量vs高清质量
   - 较小尺寸vs较大尺寸
   - 批量生成vs单张生成

2. **缓存策略**
   - 避免重复生成相同内容
   - 本地保存生成结果
   - 合理使用演示模式

## 🆘 获取帮助

### 官方支持

- **OpenAI**: [Help Center](https://help.openai.com/)
- **Stability AI**: [Documentation](https://platform.stability.ai/docs)

### 社区资源

- **OpenAI Community**: [community.openai.com](https://community.openai.com/)
- **Stability AI Discord**: [discord.gg/stablediffusion](https://discord.gg/stablediffusion)

### 应用支持

如果在使用本应用时遇到问题，请：

1. 检查浏览器控制台错误
2. 验证API密钥配置
3. 查看网络连接状态
4. 参考故障排除指南
5. 提交GitHub Issue

---

**祝您使用愉快！** 🎨
