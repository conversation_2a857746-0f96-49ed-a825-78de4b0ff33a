# TensorFlow.js 功能指南

本指南详细介绍了应用中TensorFlow.js的功能和使用方法。

## 🧠 TensorFlow.js 概述

TensorFlow.js是Google开发的JavaScript机器学习库，可以在浏览器和Node.js中运行。在我们的应用中，它主要用于：

- 图像分析和分类
- 图像预处理和后处理
- 滤镜效果应用
- 颜色分析
- 纹理分析

## 🚀 初始化和配置

### 自动后端选择

应用会自动选择最佳的计算后端：

```javascript
// 优先级: WebGL > CPU
await tf.setBackend('webgl');
await tf.ready();
```

### 支持的后端

1. **WebGL后端** (推荐)
   - 利用GPU加速
   - 适合图像处理任务
   - 需要浏览器支持WebGL

2. **CPU后端** (备选)
   - 纯JavaScript实现
   - 兼容性最好
   - 性能相对较低

## 🖼️ 图像分析功能

### 基本信息分析

```javascript
const basicInfo = {
    width: 1024,           // 图像宽度
    height: 1024,          // 图像高度
    aspectRatio: "1.00",   // 宽高比
    size: "1024×1024",     // 尺寸字符串
    type: "PNG"            // 图像格式
};
```

### 颜色分析

```javascript
const colorAnalysis = {
    averageColor: {
        r: 128,                    // 红色分量
        g: 156,                    // 绿色分量
        b: 200,                    // 蓝色分量
        hex: "#809cc8"             // 十六进制颜色
    },
    dominantChannel: "Blue",       // 主导颜色通道
    brightness: 161,               // 亮度值 (0-255)
    colorVariance: {               // 颜色方差
        r: 45,
        g: 52,
        b: 38
    }
};
```

### 内容分类

应用使用基于颜色和纹理的简化分类算法：

```javascript
const classifications = [
    {
        label: "自然风景",
        confidence: "78.50",
        description: "包含自然元素的场景"
    },
    {
        label: "艺术作品",
        confidence: "72.30",
        description: "色彩丰富的艺术创作"
    }
];
```

### 统计信息

```javascript
const statistics = {
    meanIntensity: "142.35",       // 平均强度
    standardDeviation: "45.67",    // 标准差
    minValue: 0,                   // 最小像素值
    maxValue: 255,                 // 最大像素值
    pixelCount: 1048576,           // 像素总数
    channels: 3                    // 颜色通道数
};
```

## 🎨 图像滤镜功能

### 支持的滤镜

1. **灰度滤镜**
   ```javascript
   const grayImage = await imageProcessor.applyFilter(image, 'grayscale');
   ```

2. **棕褐色滤镜**
   ```javascript
   const sepiaImage = await imageProcessor.applyFilter(image, 'sepia');
   ```

3. **模糊滤镜**
   ```javascript
   const blurredImage = await imageProcessor.applyFilter(image, 'blur');
   ```

4. **锐化滤镜**
   ```javascript
   const sharpenedImage = await imageProcessor.applyFilter(image, 'sharpen');
   ```

5. **边缘检测**
   ```javascript
   const edgeImage = await imageProcessor.applyFilter(image, 'edge');
   ```

### 滤镜实现原理

#### 灰度转换
```javascript
// 使用感知亮度公式
const weights = [0.299, 0.587, 0.114]; // R, G, B权重
const gray = R * 0.299 + G * 0.587 + B * 0.114;
```

#### 卷积滤镜
```javascript
// 3x3模糊核
const blurKernel = [
    [1/9, 1/9, 1/9],
    [1/9, 1/9, 1/9],
    [1/9, 1/9, 1/9]
];

// 3x3锐化核
const sharpenKernel = [
    [ 0, -1,  0],
    [-1,  5, -1],
    [ 0, -1,  0]
];
```

## 📊 性能优化

### 内存管理

```javascript
// 使用tf.tidy()自动清理中间张量
const result = tf.tidy(() => {
    const tensor = tf.browser.fromPixels(image);
    const processed = processImage(tensor);
    return processed;
});

// 手动清理张量
tensor.dispose();
```

### 批处理优化

```javascript
// 批量处理多张图片
const batchSize = 4;
const imageBatch = tf.stack(imageArray);
const results = model.predict(imageBatch);
```

### 模型缓存

```javascript
// 缓存已加载的模型
const modelCache = new Map();

async function loadModel(url) {
    if (modelCache.has(url)) {
        return modelCache.get(url);
    }
    
    const model = await tf.loadLayersModel(url);
    modelCache.set(url, model);
    return model;
}
```

## 🔧 自定义扩展

### 添加新的分析功能

```javascript
class CustomImageProcessor extends ImageProcessor {
    async analyzeTexture(image) {
        return tf.tidy(() => {
            const tensor = tf.browser.fromPixels(image);
            // 自定义纹理分析逻辑
            return textureFeatures;
        });
    }
}
```

### 添加新的滤镜

```javascript
// 在imageProcessor.js中添加
applyCustomFilter(tensor) {
    // 自定义滤镜实现
    const kernel = tf.tensor4d([...]);
    const filtered = tf.conv2d(tensor, kernel, 1, 'same');
    return filtered;
}
```

### 集成预训练模型

```javascript
async function loadPretrainedModel() {
    // 从TensorFlow Hub加载模型
    const model = await tf.loadLayersModel(
        'https://tfhub.dev/google/imagenet/mobilenet_v2_100_224/classification/4',
        { fromTFHub: true }
    );
    return model;
}
```

## 🐛 常见问题

### WebGL相关问题

**问题**: WebGL上下文丢失
```javascript
// 解决方案: 监听上下文丢失事件
canvas.addEventListener('webglcontextlost', (event) => {
    event.preventDefault();
    console.warn('WebGL context lost, reinitializing...');
    // 重新初始化TensorFlow.js
});
```

**问题**: 内存不足
```javascript
// 解决方案: 定期清理内存
setInterval(() => {
    const memInfo = tf.memory();
    if (memInfo.numTensors > 100) {
        console.warn('Too many tensors, cleaning up...');
        // 清理不必要的张量
    }
}, 10000);
```

### 性能问题

**问题**: 处理大图片时卡顿
```javascript
// 解决方案: 图片预处理
function resizeImage(image, maxSize = 1024) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    const scale = Math.min(maxSize / image.width, maxSize / image.height);
    canvas.width = image.width * scale;
    canvas.height = image.height * scale;
    
    ctx.drawImage(image, 0, 0, canvas.width, canvas.height);
    return canvas;
}
```

**问题**: 模型加载慢
```javascript
// 解决方案: 预加载和缓存
async function preloadModels() {
    const modelUrls = [
        'model1.json',
        'model2.json'
    ];
    
    const loadPromises = modelUrls.map(url => 
        tf.loadLayersModel(url).catch(err => {
            console.warn(`Failed to preload ${url}:`, err);
            return null;
        })
    );
    
    await Promise.all(loadPromises);
}
```

## 📈 监控和调试

### 内存监控

```javascript
function monitorMemory() {
    const memInfo = tf.memory();
    console.log('TensorFlow.js Memory:', {
        numTensors: memInfo.numTensors,
        numDataBuffers: memInfo.numDataBuffers,
        numBytes: memInfo.numBytes,
        unreliable: memInfo.unreliable
    });
}

// 每10秒监控一次
setInterval(monitorMemory, 10000);
```

### 性能分析

```javascript
async function profileOperation(operation) {
    const startTime = performance.now();
    
    const result = await tf.profile(operation);
    
    const endTime = performance.now();
    
    console.log('Operation Profile:', {
        totalTime: endTime - startTime,
        kernelMs: result.kernelMs,
        peakBytes: result.peakBytes
    });
    
    return result.result;
}
```

### 调试工具

```javascript
// 可视化张量
function visualizeTensor(tensor, name) {
    if (tensor.rank === 3) {
        const canvas = document.createElement('canvas');
        tf.browser.toPixels(tensor, canvas);
        canvas.title = name;
        document.body.appendChild(canvas);
    }
}

// 打印张量信息
function debugTensor(tensor, name) {
    console.log(`${name}:`, {
        shape: tensor.shape,
        dtype: tensor.dtype,
        size: tensor.size
    });
}
```

## 🔮 未来扩展

### 可能的功能增强

1. **更多预训练模型**
   - 物体检测 (COCO-SSD)
   - 人脸识别 (BlazeFace)
   - 姿态估计 (PoseNet)

2. **高级图像处理**
   - 图像超分辨率
   - 风格迁移
   - 图像修复

3. **实时处理**
   - 摄像头实时分析
   - 视频处理
   - 增强现实效果

### 集成建议

```javascript
// 模块化设计
class AdvancedImageProcessor {
    constructor() {
        this.models = new Map();
        this.processors = new Map();
    }
    
    async loadModel(name, url) {
        const model = await tf.loadLayersModel(url);
        this.models.set(name, model);
    }
    
    registerProcessor(name, processor) {
        this.processors.set(name, processor);
    }
    
    async process(image, processorName, options = {}) {
        const processor = this.processors.get(processorName);
        return await processor(image, options);
    }
}
```

---

**探索TensorFlow.js的无限可能！** 🚀🧠
