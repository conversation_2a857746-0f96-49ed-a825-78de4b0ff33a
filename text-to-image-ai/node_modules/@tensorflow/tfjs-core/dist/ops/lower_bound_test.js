/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
describeWithFlags('lowerBound', ALL_ENVS, () => {
    it('test1D', async () => {
        // Tests against numpy generated data.
        const NUMPY_DATA = {
            'float32': [
                [
                    -945.2247924804688, -921.8904418945312, -829.9115600585938,
                    -719.2261352539062, -660.3391723632812, -603.7969970703125,
                    -591.0955200195312, -373.1516418457031, -165.39039611816406,
                    -161.61097717285156, 117.37965393066406, 340.9350280761719,
                    370.4389953613281, 384.6452331542969, 601.4891357421875,
                    752.**********, 756.23486328125, 756.2850341796875,
                    789.2133178710938, 936.5231323242188
                ],
                [
                    -165.95599365234375, 440.64898681640625, -999.771240234375,
                    -395.3348693847656, -706.4882202148438, -815.3228149414062,
                    -627.4795532226562, -308.8785400390625, -206.46505737304688,
                    77.63346862792969
                ],
                [8, 14, 0, 7, 4, 3, 5, 8, 8, 10]
            ],
            'int32': [
                [
                    -961, -893, -793, -739, -706, -576, -468, -439, -424, -412,
                    -104, -16, 148, 178, 357, 399, 496, 578, 817, 977
                ],
                [-803, -157, 915, 66, 383, -368, 373, 669, -963, 500],
                [2, 10, 19, 12, 15, 10, 15, 18, 0, 17]
            ],
        };
        for (const dtype of ['float32', 'int32']) {
            const [sortedSequence, values, npAns] = NUMPY_DATA[dtype];
            const result = tf.lowerBound(sortedSequence, values);
            expectArraysClose(await result.data(), npAns);
        }
    });
    it('lowerBound2D', async () => {
        for (const dtype of ['float32', 'int32']) {
            const sortedSequence = tf.tensor2d([[0, 3, 9, 9, 10], [1, 2, 3, 4, 5]], undefined, dtype);
            const values = tf.tensor2d([[2, 4, 9], [0, 2, 6]], undefined, dtype);
            const correctAns = [[1, 2, 2], [0, 1, 5]];
            const result = tf.lowerBound(sortedSequence, values);
            expectArraysClose(await result.data(), correctAns);
        }
    });
});
//# sourceMappingURL=data:application/json;base64,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