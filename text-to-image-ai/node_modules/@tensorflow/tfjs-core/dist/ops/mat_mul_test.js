/**
 * @license
 * Copyright 2017 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
// Empirically determined minimal shared dimension in matmul before we forward
// to a.mul(b).sum() in order to take advantage of GPU parallelism. See
// https://github.com/tensorflow/tfjs-core/pull/1379 for benchmarks.
// Copied from webgl backend.
// TODO(yassogba, annyuan) copy tests over to webgl backend that want to
// explicitly test this threshold.
export const MATMUL_SHARED_DIM_THRESHOLD = 1000;
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose, expectArraysEqual } from '../test_util';
describeWithFlags('matmul', ALL_ENVS, () => {
    it('A x B', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const c = tf.matMul(a, b);
        expect(c.shape).toEqual([2, 2]);
        expectArraysClose(await c.data(), [0, 8, -3, 20]);
    });
    it('[8,4]x[4,8]', async () => {
        const a = tf.tensor2d([
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,
            17, 18, 19, 20, 21, 22, 23, 24, 1, 2, 3, 4, 5, 6, 7, 8
        ], [8, 4]);
        const b = tf.tensor2d([
            0, 1, -3, 2, 1, -1, 0, 5, 6, 7, 8, 0, -2, -2, 1, 9,
            11, 10, 0, 1, -3, 2, 1, -1, 1, 2, 3, 4, 5, 6, 7, 8
        ], [4, 8]);
        const c = tf.matMul(a, b);
        const cData = await c.data();
        expect(c.shape).toEqual([8, 8]);
        expectArraysClose(cData, [
            49, 53, 25, 21, 8, 25, 33, 52, 121, 133, 57, 49, 12,
            45, 69, 136, 193, 213, 89, 77, 16, 65, 105, 220, 265, 293,
            121, 105, 20, 85, 141, 304, 337, 373, 153, 133, 24, 105, 177,
            388, 409, 453, 185, 161, 28, 125, 213, 472, 49, 53, 25, 21,
            8, 25, 33, 52, 121, 133, 57, 49, 12, 45, 69, 136
        ]);
    });
    it('broadcast with unequal batch dims', async () => {
        const a = tf.tensor3d([
            2, 1, 3, 2, 1, 1, 1, 5, 6, 7, 8, 1,
            2, 2, 1, 9, 11, 10, 1, 1, 3, 2, 1, 1
        ], [4, 3, 2]);
        const b = tf.tensor3d([1, 0.5], [1, 2, 1]);
        const c = tf.matMul(a, b);
        expect(c.shape).toEqual([4, 3, 1]);
        expectArraysClose(await c.data(), [2.5, 4, 1.5, 3.5, 9.5, 8.5, 3, 5.5, 16, 1.5, 4, 1.5]);
    });
    it('broadcast with unequal ranks', async () => {
        const a = tf.tensor5d([
            2, 1, 3, 2, 1, 1, 1, 5, 6, 7, 8, 1,
            2, 2, 1, 9, 11, 10, 1, 1, 3, 2, 1, 1
        ], [1, 2, 2, 3, 2]);
        const b = tf.tensor2d([1, 0.5], [2, 1]);
        const c = tf.matMul(a, b);
        expect(c.shape).toEqual([1, 2, 2, 3, 1]);
        expectArraysClose(await c.data(), [2.5, 4, 1.5, 3.5, 9.5, 8.5, 3, 5.5, 16, 1.5, 4, 1.5]);
    });
    it('matmul followed by mul', async () => {
        const a = tf.tensor2d([1, 2, 3, 4], [2, 2]);
        const b = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const c = tf.matMul(a, b);
        const f = tf.tensor2d([0, 1, 0.5, 0, 0.25, 2], [2, 3]);
        const d = tf.mul(c, f);
        const dData = await d.data();
        expect(d.shape).toEqual([2, 3]);
        expectArraysClose(dData, [0, 12, 7.5, 0, 6.5, 66]);
    });
    it('upcasts when dtypes dont match', async () => {
        const a = [1, 2, 3, 4, 5, 6];
        const b = [0, 1, -3, 2, 2, 1];
        let c = tf.matMul(tf.tensor(a, [2, 3], 'float32'), tf.tensor(b, [3, 2], 'int32'));
        expect(c.shape).toEqual([2, 2]);
        expect(c.dtype).toBe('float32');
        expectArraysClose(await c.data(), [0, 8, -3, 20]);
        c = tf.matMul(tf.tensor(a, [2, 3], 'int32'), tf.tensor(b, [3, 2], 'bool'));
        expect(c.shape).toEqual([2, 2]);
        expect(c.dtype).toBe('int32');
        expectArraysClose(await c.data(), [5, 6, 11, 15]);
    });
    it('A x B^t', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([1, 0, 2, 4, 3, 0], [2, 3]);
        const transposeA = false;
        const transposeB = true;
        const c = tf.matMul(a, b, transposeA, transposeB);
        const expected = [7, 10, 16, 31];
        expectArraysClose(await c.data(), expected);
    });
    it('A^t x B', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([1, 0, 2, 4, 3, 0], [2, 3]);
        const transposeA = true;
        const transposeB = false;
        const c = tf.matMul(a, b, transposeA, transposeB);
        const expected = [17, 12, 2, 22, 15, 4, 27, 18, 6];
        expectArraysClose(await c.data(), expected);
    });
    it('A^t x B^t', async () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [3, 2]);
        const b = tf.tensor2d([1, 0, 2, 4, 3, 0], [2, 3]);
        const transposeA = true;
        const transposeB = true;
        const c = tf.matMul(a, b, transposeA, transposeB);
        const expected = [11, 13, 14, 20];
        expectArraysClose(await c.data(), expected);
    });
    it('A x B^t shapes do not match', () => {
        const a = tf.zeros([2, 3]);
        const b = tf.zeros([3, 2]);
        const f = () => {
            const transposeA = false;
            const transposeB = true;
            tf.matMul(a, b, transposeA, transposeB);
        };
        expect(f).toThrowError();
    });
    it('A^t x B shapes do not match', () => {
        const a = tf.zeros([2, 3]);
        const b = tf.zeros([3, 2]);
        const f = () => {
            const transposeA = true;
            const transposeB = false;
            tf.matMul(a, b, transposeA, transposeB);
        };
        expect(f).toThrowError();
    });
    it('A^t x B^t shapes do not match', () => {
        const a = tf.zeros([3, 2]);
        const b = tf.zeros([3, 2]);
        const f = () => {
            const transposeA = true;
            const transposeB = true;
            tf.matMul(a, b, transposeA, transposeB);
        };
        expect(f).toThrowError();
    });
    it('matmul throws when inner dimensions dont match', () => {
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1, 2, 2], [4, 2]);
        expect(() => tf.matMul(a, b)).toThrowError();
    });
    it('matmul throws when passed non matrices', () => {
        // tslint:disable-next-line:no-any
        const a = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [2, 3, 2]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1, 2, 2], [4, 2]);
        expect(() => tf.matMul(a, b)).toThrowError();
        expect(() => tf.matMul(b, a)).toThrowError();
    });
    it('matmul throws when passed a vector', () => {
        // tslint:disable-next-line:no-any
        const v = tf.tensor1d([2, 3]);
        const matrix = tf.tensor2d([1, 2, 3, 4], [2, 2]);
        expect(() => tf.matMul(matrix, v)).toThrowError();
    });
    it('Vector times matrix', async () => {
        const v = tf.tensor1d([2, 3]);
        const matrix = tf.tensor2d([1, 2, 3, 4], [2, 2]);
        const result = tf.dot(v, matrix);
        const expected = [11, 16];
        expectArraysClose(await result.data(), expected);
    });
    it('Vector times matrix with implicit reshape', async () => {
        const v = tf.tensor1d([2, 3]);
        const matrix = tf.tensor2d([1, 2, 3, 4], [2, 2]);
        const result = tf.dot(v, matrix);
        const expected = [11, 16];
        expectArraysClose(await result.data(), expected);
    });
    it('Matrix times vector', async () => {
        const matrix = tf.tensor2d([1, 2, 3, 4], [2, 2]);
        const v = tf.tensor1d([2, 3]);
        const result = tf.dot(matrix, v);
        const expected = [8, 18];
        expectArraysClose(await result.data(), expected);
    });
    it('batched matmul with the matrices being vectors', async () => {
        const batch = 3;
        const sharedDim = MATMUL_SHARED_DIM_THRESHOLD + 1;
        const values = new Float32Array(batch * sharedDim);
        values[10] = 2;
        const a = tf.tensor(values, [batch, 1, sharedDim]);
        const b = tf.tensor(values, [batch, sharedDim, 1]);
        const result = tf.matMul(a, b);
        expect(result.shape).toEqual([batch, 1, 1]);
        expectArraysClose(await result.data(), [4, 0, 0]);
    });
    it('batched matmul called twice so memory of output is reused', async () => {
        const batch = 3;
        const n = 2;
        const vals = new Float32Array(batch * n * n);
        vals[0] = 2;
        vals[4] = 3;
        vals[8] = 4;
        const a = tf.tensor(vals, [batch, n, n]);
        const b = tf.tensor(vals, [batch, n, n]);
        const result = tf.matMul(a, b);
        expect(result.shape).toEqual([batch, n, n]);
        expectArraysClose(await result.data(), [4, 0, 0, 0, 9, 0, 0, 0, 16, 0, 0, 0]);
        // Dispose the first output, so memory of the second output (which has the
        // same shape), could be reused.
        result.dispose();
        const vals2 = new Float32Array(batch * n * n);
        vals2[3] = 2;
        vals2[7] = 3;
        vals2[11] = 4;
        const a2 = tf.tensor(vals2, [batch, n, n]);
        const b2 = tf.tensor(vals2, [batch, n, n]);
        const result2 = tf.matMul(a2, b2);
        expect(result2.shape).toEqual([batch, n, n]);
        expectArraysClose(await result2.data(), [0, 0, 0, 4, 0, 0, 0, 9, 0, 0, 0, 16]);
    });
    it('batched matmul with the matrices being vectors transposedA', async () => {
        const batch = 3;
        const sharedDim = MATMUL_SHARED_DIM_THRESHOLD + 1;
        const values = new Float32Array(batch * sharedDim);
        values[10] = 2;
        const a = tf.tensor(values, [batch, sharedDim, 1]);
        const b = tf.tensor(values, [batch, sharedDim, 1]);
        const transposeA = true;
        const transposeB = false;
        const result = tf.matMul(a, b, transposeA, transposeB);
        expect(result.shape).toEqual([batch, 1, 1]);
        expectArraysClose(await result.data(), [4, 0, 0]);
    });
    it('batched matmul with the matrices being vectors transposedB', async () => {
        const batch = 3;
        const sharedDim = MATMUL_SHARED_DIM_THRESHOLD + 1;
        const values = new Float32Array(batch * sharedDim);
        values[10] = 2;
        const a = tf.tensor(values, [batch, 1, sharedDim]);
        const b = tf.tensor(values, [batch, 1, sharedDim]);
        const transposeA = false;
        const transposeB = true;
        const result = tf.matMul(a, b, transposeA, transposeB);
        expect(result.shape).toEqual([batch, 1, 1]);
        expectArraysClose(await result.data(), [4, 0, 0]);
    });
    it('batched matmul with matrix x vector', async () => {
        const batch = 3;
        const sharedDim = MATMUL_SHARED_DIM_THRESHOLD + 1;
        const values = new Float32Array(batch * sharedDim);
        values[10] = 2;
        const a = tf.ones([batch, 2, sharedDim]);
        const b = tf.tensor(values, [batch, sharedDim, 1]);
        const result = tf.matMul(a, b);
        expect(result.shape).toEqual([batch, 2, 1]);
        expectArraysClose(await result.data(), [2, 2, 0, 0, 0, 0]);
    });
    it('batched matmul with matrix x vector transposedA', async () => {
        const batch = 3;
        const sharedDim = MATMUL_SHARED_DIM_THRESHOLD + 1;
        const values = new Float32Array(batch * sharedDim);
        values[10] = 2;
        const a = tf.ones([batch, sharedDim, 2]);
        const b = tf.tensor(values, [batch, sharedDim, 1]);
        const transposeA = true;
        const transposeB = false;
        const result = tf.matMul(a, b, transposeA, transposeB);
        expect(result.shape).toEqual([batch, 2, 1]);
        expectArraysClose(await result.data(), [2, 2, 0, 0, 0, 0]);
    });
    it('batched matmul with matrix x vector transposedB', async () => {
        const batch = 3;
        const sharedDim = MATMUL_SHARED_DIM_THRESHOLD + 1;
        const values = new Float32Array(batch * sharedDim);
        values[10] = 2;
        const a = tf.ones([batch, 2, sharedDim]);
        const b = tf.tensor(values, [batch, 1, sharedDim]);
        const transposeA = false;
        const transposeB = true;
        const result = tf.matMul(a, b, transposeA, transposeB);
        expect(result.shape).toEqual([batch, 2, 1]);
        expectArraysClose(await result.data(), [2, 2, 0, 0, 0, 0]);
    });
    it('batched matmul with vector x matrix', async () => {
        const batch = 3;
        const sharedDim = MATMUL_SHARED_DIM_THRESHOLD + 1;
        const values = new Float32Array(batch * sharedDim);
        values[10] = 2;
        const a = tf.tensor(values, [batch, 1, sharedDim]);
        const b = tf.ones([batch, sharedDim, 2]);
        const result = tf.matMul(a, b);
        expect(result.shape).toEqual([batch, 1, 2]);
        expectArraysClose(await result.data(), [2, 2, 0, 0, 0, 0]);
    });
    it('batched matmul with vector x matrix transposedA', async () => {
        const batch = 3;
        const sharedDim = MATMUL_SHARED_DIM_THRESHOLD + 1;
        const values = new Float32Array(batch * sharedDim);
        values[10] = 2;
        const a = tf.tensor(values, [batch, sharedDim, 1]);
        const b = tf.ones([batch, sharedDim, 2]);
        const transposeA = true;
        const transposeB = false;
        const result = tf.matMul(a, b, transposeA, transposeB);
        expect(result.shape).toEqual([batch, 1, 2]);
        expectArraysClose(await result.data(), [2, 2, 0, 0, 0, 0]);
    });
    it('batched matmul with vector x matrix transposedB', async () => {
        const batch = 3;
        const sharedDim = MATMUL_SHARED_DIM_THRESHOLD + 1;
        const values = new Float32Array(batch * sharedDim);
        values[10] = 2;
        const a = tf.tensor(values, [batch, 1, sharedDim]);
        const b = tf.ones([batch, 2, sharedDim]);
        const transposeA = false;
        const transposeB = true;
        const result = tf.matMul(a, b, transposeA, transposeB);
        expect(result.shape).toEqual([batch, 1, 2]);
        expectArraysClose(await result.data(), [2, 2, 0, 0, 0, 0]);
    });
    it('Matrix * vector propagates NaNs', async () => {
        const matrix = tf.tensor2d([1, 2, 3, 4], [2, 2]);
        const v = tf.tensor1d([2, NaN]);
        const result = tf.dot(matrix, v);
        const expected = [NaN, NaN];
        expectArraysClose(await result.data(), expected);
    });
    it('matrix times vector throws when not passed a matrix', () => {
        const v = tf.tensor1d([2, 3]);
        // tslint:disable-next-line:no-any
        const matrix = tf.tensor3d([1, 2, 3, 4, 5, 6, 7, 8], [2, 2, 2]);
        expect(() => tf.dot(matrix, v)).toThrowError();
    });
    it('Dot product', async () => {
        const v1 = tf.tensor1d([2, 3]);
        const v2 = tf.tensor1d([2, 1]);
        const result = tf.dot(v1, v2);
        expectArraysClose(await result.data(), [7]);
    });
    it('Dot product propagates NaNs', async () => {
        const v1 = tf.tensor1d([2, NaN]);
        const v2 = tf.tensor1d([2, 1]);
        const result = tf.dot(v1, v2);
        expectArraysEqual(await result.data(), [NaN]);
    });
    it('Dot product throws when vectors are different size', () => {
        const v1 = tf.tensor1d([2, 3, 3]);
        const v2 = tf.tensor1d([2, 1]);
        expect(() => tf.dot(v1, v2)).toThrowError();
        expect(() => tf.dot(v2, v1)).toThrowError();
    });
    it('Outer product', async () => {
        const v1 = tf.tensor1d([2, 3]);
        const v2 = tf.tensor1d([2, 1]);
        const result = tf.outerProduct(v1, v2);
        const expected = [4, 2, 6, 3];
        expect(result.shape).toEqual([2, 2]);
        expectArraysClose(await result.data(), expected);
    });
    it('outer product accepts a tensor-like object', async () => {
        const v1 = [2, 3];
        const v2 = [2, 1];
        const result = tf.outerProduct(v1, v2);
        const expected = [4, 2, 6, 3];
        expect(result.shape).toEqual([2, 2]);
        expectArraysClose(await result.data(), expected);
    });
    it('gradients: A * B', async () => {
        const aT = tf.tensor2d([1, 2, 3, 10, 20, 30], [2, 3]);
        const bT = tf.tensor2d([2, 3, 4, 1, 2, 3], [3, 2]);
        const dyT = tf.tensor2d([1, 10, 20, 30], [2, 2]);
        const transposeA = false;
        const transposeB = false;
        const grads = tf.grads((a, b) => tf.matMul(a, b, transposeA, transposeB));
        const [da, db] = grads([aT, bT], dyT);
        // da = dy * bT
        expect(da.shape).toEqual(aT.shape);
        const a = await aT.buffer();
        const dy = await dyT.buffer();
        const b = await bT.buffer();
        expectArraysClose(await da.data(), [
            dy.get(0, 0) * b.get(0, 0) + dy.get(0, 1) * b.get(0, 1),
            dy.get(0, 0) * b.get(1, 0) + dy.get(0, 1) * b.get(1, 1),
            dy.get(0, 0) * b.get(2, 0) + dy.get(0, 1) * b.get(2, 1),
            dy.get(1, 0) * b.get(0, 0) + dy.get(1, 1) * b.get(0, 1),
            dy.get(1, 0) * b.get(1, 0) + dy.get(1, 1) * b.get(1, 1),
            dy.get(1, 0) * b.get(2, 0) + dy.get(1, 1) * b.get(2, 1)
        ], 1e-1);
        // db = aT * dy
        expect(db.shape).toEqual(b.shape);
        expectArraysClose(await db.data(), [
            a.get(0, 0) * dy.get(0, 0) + a.get(1, 0) * dy.get(1, 0),
            a.get(0, 0) * dy.get(0, 1) + a.get(1, 0) * dy.get(1, 1),
            a.get(0, 1) * dy.get(0, 0) + a.get(1, 1) * dy.get(1, 0),
            a.get(0, 1) * dy.get(0, 1) + a.get(1, 1) * dy.get(1, 1),
            a.get(0, 2) * dy.get(0, 0) + a.get(1, 2) * dy.get(1, 0),
            a.get(0, 2) * dy.get(0, 1) + a.get(1, 2) * dy.get(1, 1)
        ]);
    });
    it('gradient with clones', () => {
        const a = tf.tensor2d([1, 2, 3, 10, 20, 30], [2, 3]);
        const b = tf.tensor2d([2, 3, 4, 1, 2, 3], [3, 2]);
        const grads = tf.grads((a, b) => tf.matMul(a.clone(), b.clone()).clone());
        const [da, db] = grads([a, b]);
        expect(da.shape).toEqual(a.shape);
        expect(db.shape).toEqual(b.shape);
    });
    it('gradients: a * bT', async () => {
        const aT = tf.tensor2d([1, 2, 3, 10, 20, 30], [3, 2]);
        const bT = tf.tensor2d([2, 3, 4, 1, 2, 3], [3, 2]);
        const dyT = tf.tensor2d([1, 10, 20, 30, 40, 50, 60, 70, 80], [3, 3]);
        const transposeA = false;
        const transposeB = true;
        const grads = tf.grads((a, b) => tf.matMul(a, b, transposeA, transposeB));
        const [da, db] = grads([aT, bT], dyT);
        // da = dy * b
        expect(da.shape).toEqual(aT.shape);
        const a = await aT.buffer();
        const dy = await dyT.buffer();
        const b = await bT.buffer();
        expectArraysClose(await da.data(), [
            dy.get(0, 0) * b.get(0, 0) + dy.get(0, 1) * b.get(1, 0) +
                dy.get(0, 2) * b.get(2, 0),
            dy.get(0, 0) * b.get(0, 1) + dy.get(0, 1) * b.get(1, 1) +
                dy.get(0, 2) * b.get(2, 1),
            dy.get(1, 0) * b.get(0, 0) + dy.get(1, 1) * b.get(1, 0) +
                dy.get(1, 2) * b.get(2, 0),
            dy.get(1, 0) * b.get(0, 1) + dy.get(1, 1) * b.get(1, 1) +
                dy.get(1, 2) * b.get(2, 1),
            dy.get(2, 0) * b.get(0, 0) + dy.get(2, 1) * b.get(1, 0) +
                dy.get(2, 2) * b.get(2, 0),
            dy.get(2, 0) * b.get(0, 1) + dy.get(2, 1) * b.get(1, 1) +
                dy.get(2, 2) * b.get(2, 1)
        ]);
        // db = dyT * a
        expect(db.shape).toEqual(b.shape);
        expectArraysClose(await db.data(), [
            dy.get(0, 0) * a.get(0, 0) + dy.get(1, 0) * a.get(1, 0) +
                dy.get(2, 0) * a.get(2, 0),
            dy.get(0, 0) * a.get(0, 1) + dy.get(1, 0) * a.get(1, 1) +
                dy.get(2, 0) * a.get(2, 1),
            dy.get(0, 1) * a.get(0, 0) + dy.get(1, 1) * a.get(1, 0) +
                dy.get(2, 1) * a.get(2, 0),
            dy.get(0, 1) * a.get(0, 1) + dy.get(1, 1) * a.get(1, 1) +
                dy.get(2, 1) * a.get(2, 1),
            dy.get(0, 2) * a.get(0, 0) + dy.get(1, 2) * a.get(1, 0) +
                dy.get(2, 2) * a.get(2, 0),
            dy.get(0, 2) * a.get(0, 1) + dy.get(1, 2) * a.get(1, 1) +
                dy.get(2, 2) * a.get(2, 1)
        ]);
    });
    it('gradients: aT * b', async () => {
        const aT = tf.tensor2d([1, 2, 3, 10, 20, 30], [3, 2]);
        const bT = tf.tensor2d([2, 3, 4, 1, 2, 3], [3, 2]);
        const dyT = tf.tensor2d([1, 10, 20, 30], [2, 2]);
        const transposeA = true;
        const transposeB = false;
        const grads = tf.grads((a, b) => tf.matMul(a, b, transposeA, transposeB));
        const [da, db] = grads([aT, bT], dyT);
        // da = b * dyT
        expect(da.shape).toEqual(aT.shape);
        const a = await aT.buffer();
        const dy = await dyT.buffer();
        const b = await bT.buffer();
        expectArraysClose(await da.data(), [
            dy.get(0, 0) * b.get(0, 0) + dy.get(0, 1) * b.get(0, 1),
            dy.get(1, 0) * b.get(0, 0) + dy.get(1, 1) * b.get(0, 1),
            dy.get(0, 0) * b.get(1, 0) + dy.get(0, 1) * b.get(1, 1),
            dy.get(1, 0) * b.get(1, 0) + dy.get(1, 1) * b.get(1, 1),
            dy.get(0, 0) * b.get(2, 0) + dy.get(0, 1) * b.get(2, 1),
            dy.get(1, 0) * b.get(2, 0) + dy.get(1, 1) * b.get(2, 1)
        ]);
        // db = a * dy
        expect(db.shape).toEqual(b.shape);
        expectArraysClose(await db.data(), [
            dy.get(0, 0) * a.get(0, 0) + dy.get(1, 0) * a.get(0, 1),
            dy.get(0, 1) * a.get(0, 0) + dy.get(1, 1) * a.get(0, 1),
            dy.get(0, 0) * a.get(1, 0) + dy.get(1, 0) * a.get(1, 1),
            dy.get(0, 1) * a.get(1, 0) + dy.get(1, 1) * a.get(1, 1),
            dy.get(0, 0) * a.get(2, 0) + dy.get(1, 0) * a.get(2, 1),
            dy.get(0, 1) * a.get(2, 0) + dy.get(1, 1) * a.get(2, 1)
        ]);
    });
    it('gradients: aT * bT', async () => {
        const aT = tf.tensor2d([1, 2, 3, 10, 20, 30], [3, 2]);
        const bT = tf.tensor2d([2, 3, 4, 1, 2, 3], [2, 3]);
        const dyT = tf.tensor2d([1, 10, 20, 30], [2, 2]);
        const transposeA = true;
        const transposeB = true;
        const grads = tf.grads((a, b) => tf.matMul(a, b, transposeA, transposeB));
        const [da, db] = grads([aT, bT], dyT);
        // da = bT * dyT
        expect(da.shape).toEqual(aT.shape);
        const a = await aT.buffer();
        const dy = await dyT.buffer();
        const b = await bT.buffer();
        expectArraysClose(await da.data(), [
            dy.get(0, 0) * b.get(0, 0) + dy.get(0, 1) * b.get(1, 0),
            dy.get(1, 0) * b.get(0, 0) + dy.get(1, 1) * b.get(1, 0),
            dy.get(0, 0) * b.get(0, 1) + dy.get(0, 1) * b.get(1, 1),
            dy.get(1, 0) * b.get(0, 1) + dy.get(1, 1) * b.get(1, 1),
            dy.get(0, 0) * b.get(0, 2) + dy.get(0, 1) * b.get(1, 2),
            dy.get(1, 0) * b.get(0, 2) + dy.get(1, 1) * b.get(1, 2)
        ]);
        // db = dyT * aT
        expect(db.shape).toEqual(b.shape);
        expectArraysClose(await db.data(), [
            dy.get(0, 0) * a.get(0, 0) + dy.get(1, 0) * a.get(0, 1),
            dy.get(0, 0) * a.get(1, 0) + dy.get(1, 0) * a.get(1, 1),
            dy.get(0, 0) * a.get(2, 0) + dy.get(1, 0) * a.get(2, 1),
            dy.get(0, 1) * a.get(0, 0) + dy.get(1, 1) * a.get(0, 1),
            dy.get(0, 1) * a.get(1, 0) + dy.get(1, 1) * a.get(1, 1),
            dy.get(0, 1) * a.get(2, 0) + dy.get(1, 1) * a.get(2, 1)
        ]);
    });
    it('throws when passed a as a non-tensor', () => {
        expect(() => tf.matMul({}, tf.tensor2d([2], [1, 1])))
            .toThrowError(/Argument 'a' passed to 'matMul' must be a Tensor/);
    });
    it('throws when passed b as a non-tensor', () => {
        expect(() => tf.matMul(tf.tensor2d([2], [1, 1]), {}))
            .toThrowError(/Argument 'b' passed to 'matMul' must be a Tensor/);
    });
    it('accepts a tensor-like object', async () => {
        const a = [[1, 2, 3], [4, 5, 6]]; // 2x3
        const b = [[0, 1], [-3, 2], [2, 1]]; // 3x2
        const c = tf.matMul(a, b);
        expect(c.shape).toEqual([2, 2]);
        expectArraysClose(await c.data(), [0, 8, -3, 20]);
    });
    it('accepts a tensor-like object chained', async () => {
        const a = tf.tensor2d([[1, 2, 3], [4, 5, 6]], [2, 3]); // 2x3
        const b = [[0, 1], [-3, 2], [2, 1]]; // 3x2
        const c = a.matMul(b);
        expect(c.shape).toEqual([2, 2]);
        expectArraysClose(await c.data(), [0, 8, -3, 20]);
    });
    it('a * b where a has zero in its shape', async () => {
        const a = tf.tensor2d([], [0, 3]);
        const b = tf.tensor2d([1, 2, 3, 4, 5, 6], [3, 2]);
        const c = tf.matMul(a, b);
        expect(c.shape).toEqual([0, 2]);
        expect(c.rank).toBe(2);
        expect(c.size).toBe(0);
        expectArraysClose(await c.data(), []);
    });
    it('(a * b) * c where a has zero in its shape, so a*b does also', async () => {
        const a = tf.tensor2d([], [0, 3]);
        const b = tf.tensor2d([1, 2, 3, 4, 5, 6], [3, 2]);
        const ab = tf.matMul(a, b);
        expect(ab.shape).toEqual([0, 2]);
        expectArraysClose(await ab.data(), []);
        const c = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const res = tf.matMul(ab, c);
        expect(res.shape).toEqual([0, 3]);
        expectArraysClose(await res.data(), []);
    });
    it('throws error for string tensor', () => {
        expect(() => tf.matMul([['a']], [['b']]))
            .toThrowError(/Argument 'a' passed to 'matMul' must be numeric tensor/);
    });
});
describeWithFlags('matmulBatch', ALL_ENVS, () => {
    it('A x B', async () => {
        const a = tf.tensor3d([
            -5, -5, -6, 8, -2, -8, 4, -7, -6, -9, -1, 3, 7, -2, 5,
            -6, 3, 8, 7, -8, 1, 4, -4, 6, 4, -4, -9, -5, 2, -2
        ], [5, 2, 3]);
        const b = tf.tensor3d([
            -8, -4, -1, 0, -7, 0, 3, 3, 6, 2, -1, 8, -4, 9, -6,
            5, 8, 9, -9, 7, 0, -1, -1, -10, -7, 3, 4, 6, 3, -4
        ], [5, 3, 2]);
        const c = tf.matMul(a, b);
        expect(c.shape).toEqual([5, 2, 2]);
        expectArraysClose(await c.data(), [
            87, 20, -6, -32, -24, -50, -36, -5, 24, 98,
            70, 33, -64, 47, -42, -28, -71, 24, 37, 5
        ]);
    });
    it('A x B in 4D', async () => {
        const a = tf.tensor4d([
            -2, 3, 5, -5, 3, 9, -3, -5, 1, 1, -9, 9, -6, 6, -8,
            -7, -1, 3, 9, -7, -7, 2, 10, -6, -8, -6, 9, -6, 4, -1,
            9, -6, 10, 8, -9, 5, -8, -7, 0, 2, -5, -1, -9, -4, 3,
            -2, 6, -4, 7, 1, -5, -4, 9, -8, -6, -8, 4, -1, 4, 3,
            -7, 8, -7, 5, -3, -2, -4, 9, 2, -1, 1, -10, -3, 5, -4,
            6, -8, -8, 9, -3, -5, 10, 3, -3, -3, 9, 3, -3, 2, -8,
            10, 1, 9, -2, -2, -3, -4, 6, -10, -1, 8, -8, 7, 3, -2,
            3, 6, -2, -2, -4, 1, -5, -4, 0, 5, 1, 9, -8, -2, -1
        ], [4, 5, 2, 3]);
        const b = tf.tensor4d([
            -4, -3, -2, -6, 6, -1, -4, -1, 7, -4, 8, -9, -9, 0, -1,
            -4, -6, -7, -3, -4, -7, 6, -8, 1, -2, 1, -1, -3, 8, -5,
            9, -2, 5, 9, -2, 2, -5, -5, -8, -1, -2, -3, -2, -10, 6,
            -3, 0, 1, 6, 7, 1, 2, -4, -5, 2, -5, -7, 9, 3, -6,
            6, 4, -4, 6, 10, -3, -2, 8, 10, -8, 10, -1, -9, -7, -8,
            -3, 1, 1, -2, -9, -7, -6, -1, 0, 7, -9, -7, -5, 0, -4,
            -4, -7, 2, 4, 6, 6, -4, -6, -8, 3, -8, -9, 6, 9, -4,
            1, -1, 0, 8, 9, 0, -5, 3, -1, 5, 0, -10, 7, -2, 6
        ], [4, 5, 3, 2]);
        const transposeA = false;
        const transposeB = false;
        const c = tf.matMul(a, b, transposeA, transposeB);
        expectArraysClose(await c.data(), [
            32, -17, 68, -12, -15, 14, 5, -46, 96, 32, 46, -17, 78, -85,
            -28, 46, 94, -35, 0, -13, 31, -52, 17, -87, 96, 47, 32, -2,
            -6, 105, 40, -2, 63, 76, 17, 30, 56, -66, -21, 23, -144, 41,
            22, 8, 118, -106, -88, -6, -17, 2, 2, -26, 8, -63, -38, -108,
            -84, -30, -35, 49, 16, -12, -14, -12, 48, 132, 4, 102, 32, 66,
            -4, 33, -13, 1, -40, -25, -3, 61, -18, -20
        ]);
    });
    it('A x B^t', async () => {
        const a = tf.tensor3d([
            -5, -5, -6, 8, -2, -8, 4, -7, -6, -9, -1, 3, 7, -2, 5,
            -6, 3, 8, 7, -8, 1, 4, -4, 6, 4, -4, -9, -5, 2, -2
        ], [5, 2, 3]);
        const b = tf.tensor3d([
            -8, -4, -1, 0, -7, 0, 3, 3, 6, 2, -1, 8, -4, 9, -6,
            5, 8, 9, -9, 7, 0, -1, -1, -10, -7, 3, 4, 6, 3, -4
        ], [5, 2, 3]);
        const transposeA = false;
        const transposeB = true;
        const c = tf.matMul(a, b, transposeA, transposeB);
        expect(c.shape).toEqual([5, 2, 2]);
        expectArraysClose(await c.data(), [
            66, 35, -48, 14, -45, -33, -12, 7, -76, 64,
            3, 66, -119, -9, -64, -60, -76, 48, 33, -16
        ]);
    });
    it('A^t x B', async () => {
        const a = tf.tensor3d([
            -5, -5, -6, 8, -2, -8, 4, -7, -6, -9, -1, 3, 7, -2, 5,
            -6, 3, 8, 7, -8, 1, 4, -4, 6, 4, -4, -9, -5, 2, -2
        ], [5, 2, 3]);
        const b = tf.tensor3d([
            -8, -4, -1, 0, -7, 0, 3, 3, 6, 2, -1, 8, -4, 9, -6,
            5, 8, 9, -9, 7, 0, -1, -1, -10, -7, 3, 4, 6, 3, -4
        ], [5, 2, 3]);
        const transposeA = true;
        const transposeB = false;
        const c = tf.matMul(a, b, transposeA, transposeB);
        expectArraysClose(await c.data(), [
            40, -36, 5, 40, 34, 5, 48, 80, 6, -6, 21, -48, -23, -20, -50,
            -12, -21, -12, -58, 15, -96, 23, 6, 39, 20, 109, 42, -67, 45, -40,
            76, -52, 40, -15, 1, -60, -58, -3, 36, 40, -6, -24, 51, -33, -28
        ]);
    });
    it('A^t x B in 4D', async () => {
        const a = tf.tensor4d([
            -2, 3, 5, -5, 3, 9, -3, -5, 1, 1, -9, 9, -6, 6, -8,
            -7, -1, 3, 9, -7, -7, 2, 10, -6, -8, -6, 9, -6, 4, -1,
            9, -6, 10, 8, -9, 5, -8, -7, 0, 2, -5, -1, -9, -4, 3,
            -2, 6, -4, 7, 1, -5, -4, 9, -8, -6, -8, 4, -1, 4, 3,
            -7, 8, -7, 5, -3, -2, -4, 9, 2, -1, 1, -10, -3, 5, -4,
            6, -8, -8, 9, -3, -5, 10, 3, -3, -3, 9, 3, -3, 2, -8,
            10, 1, 9, -2, -2, -3, -4, 6, -10, -1, 8, -8, 7, 3, -2,
            3, 6, -2, -2, -4, 1, -5, -4, 0, 5, 1, 9, -8, -2, -1
        ], [4, 5, 2, 3]);
        const b = tf.tensor4d([
            -4, -3, -2, -6, 6, -1, -4, -1, 7, -4, 8, -9, -9, 0, -1,
            -4, -6, -7, -3, -4, -7, 6, -8, 1, -2, 1, -1, -3, 8, -5,
            9, -2, 5, 9, -2, 2, -5, -5, -8, -1, -2, -3, -2, -10, 6,
            -3, 0, 1, 6, 7, 1, 2, -4, -5, 2, -5, -7, 9, 3, -6,
            6, 4, -4, 6, 10, -3, -2, 8, 10, -8, 10, -1, -9, -7, -8,
            -3, 1, 1, -2, -9, -7, -6, -1, 0, 7, -9, -7, -5, 0, -4,
            -4, -7, 2, 4, 6, 6, -4, -6, -8, 3, -8, -9, 6, 9, -4,
            1, -1, 0, 8, 9, 0, -5, 3, -1, 5, 0, -10, 7, -2, 6
        ], [4, 5, 2, 3]);
        const transposeA = true;
        const transposeB = false;
        const c = tf.matMul(a, b, transposeA, transposeB);
        expectArraysClose(await c.data(), [
            38, -24, 9, -30, 9, -9, -74, 39, -19, 8, 11, -30, 56, -67,
            46, -40, 71, -74, 82, 42, 55, -50, 6, 1, 60, -18, -13, -15,
            -52, -61, 81, -52, 59, -15, 76, 43, 34, -56, 38, 0, 26, -14,
            -15, 1, -4, 153, -34, 61, -135, 30, -48, 135, -30, 60, 38, 36,
            58, 40, 45, 71, 1, 2, 3, 24, 90, -56, -10, 40, -18, 6,
            -30, 14, 34, 65, 27, 24, -29, -44, -46, -3, 35, -21, 27, 48,
            20, 52, 32, 35, -11, -46, -12, 22, 13, 30, 2, -23, -54, -48,
            34, 16, -42, -39, -26, 82, 89, 76, -84, 30, 9, 27, 30, -21,
            -43, -48, 60, 20, 24, -78, -91, -63, -12, 24, 21, 28, 48, 35,
            -6, 27, 33, 53, -81, -71, 61, -27, 11, -48, -82, 8, -12, -19,
            -10, -48, -81, 0, 13, 32, 41, 0, -100, -120, 16, 124, 152, 45,
            60, -28, 24, 21, -12, -14, -16, 8, 9, -33, 5, -12, -48, 4,
            8, 9, 0, -31, 16, -98, -9, 4, -22, 38, 2, -96
        ]);
    });
    it('A^t x B^t', async () => {
        const a = tf.tensor3d([
            -5, -5, -6, 8, -2, -8, 4, -7, -6, -9, -1, 3, 7, -2, 5,
            -6, 3, 8, 7, -8, 1, 4, -4, 6, 4, -4, -9, -5, 2, -2
        ], [5, 3, 2]);
        const b = tf.tensor3d([
            -8, -4, -1, 0, -7, 0, 3, 3, 6, 2, -1, 8, -4, 9, -6,
            5, 8, 9, -9, 7, 0, -1, -1, -10, -7, 3, 4, 6, 3, -4
        ], [5, 2, 3]);
        const transposeA = true;
        const transposeB = true;
        const c = tf.matMul(a, b, transposeA, transposeB);
        expectArraysClose(await c.data(), [
            66, 42, 16, -56, -12, 6, -30, 19, -1, 102,
            -94, 14, -56, 32, 100, -56, -47, -11, 5, -31
        ]);
    });
    it('A has more batch dimensions than B', async () => {
        const a = tf.tensor4d([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], [2, 2, 2, 2]);
        const b = tf.tensor3d([1, 2, 3, 4], [2, 2, 1]);
        const c = tf.matMul(a, b);
        expectArraysClose(await c.data(), [5, 11, 39, 53, 29, 35, 95, 109]);
    });
    it('batch dimensions do not match', () => {
        const a = tf.tensor3d([
            -5, -5, -6, 8, -2, -8, 4, -7, -6, -9, -1, 3,
            7, -2, 5, -6, 3, 8, 7, -8, 1, 4, -4, 6
        ], [4, 3, 2]);
        const b = tf.tensor3d([
            -8, -4, -1, 0, -7, 0, 3, 3, 6, 2, -1, 8, -4, 9, -6,
            5, 8, 9, -9, 7, 0, -1, -1, -10, -7, 3, 4, 6, 3, -4
        ], [5, 2, 3]);
        const f = () => {
            tf.matMul(a, b, false, false);
        };
        expect(f).toThrowError();
    });
    it('gradients: A x B', async () => {
        const a = tf.tensor3d([
            -5, -5, -6, 8, -2, -8, 4, -7, -6, -9, -1, 3, 7, -2, 5,
            -6, 3, 8, 7, -8, 1, 4, -4, 6, 4, -4, -9, -5, 2, -2
        ], [5, 2, 3]);
        const b = tf.tensor3d([
            -8, -4, -1, 0, -7, 0, 3, 3, 6, 2, -1, 8, -4, 9, -6,
            5, 8, 9, -9, 7, 0, -1, -1, -10, -7, 3, 4, 6, 3, -4
        ], [5, 3, 2]);
        const dy = tf.tensor3d([8, 2, -3, -2, -8, 4, 5, 7, 4, -4, -4, 5, 8, 10, 1, 0, 6, 6, -4, 7], [5, 2, 2]);
        const grads = tf.grads((a, b) => tf.matMul(a, b, false, false));
        const [da, db] = grads([a, b], dy);
        // da = dy * bT
        expect(da.shape).toEqual(a.shape);
        expectArraysClose(await da.data(), [
            -72, -8, -56, 32, 3, 21, -12, -40, 40, 36, 44, 51, -52, -44, -4,
            61, 49, 13, -2, -10, -108, -9, 0, -1, -24, 60, -6, 49, 26, -40
        ]);
        // db = aT * dy
        expect(db.shape).toEqual(b.shape);
        expectArraysClose(await db.data(), [
            -64, -26, -34, -6, -24, 4, -77, -47, 51, -35, 63, -3, 52, -58, -20,
            23, -12, 20, 60, 70, -68, -80, 14, 10, 44, -11, -32, -10, -46, -68
        ]);
    });
    it('4d gradients: A x B', async () => {
        const a = tf.tensor4d([
            -2, 3, 5, -5, 3, 9, -3, -5, 1, 1, -9, 9, -6, 6, -8,
            -7, -1, 3, 9, -7, -7, 2, 10, -6, -8, -6, 9, -6, 4, -1,
            9, -6, 10, 8, -9, 5, -8, -7, 0, 2, -5, -1, -9, -4, 3,
            -2, 6, -4, 7, 1, -5, -4, 9, -8, -6, -8, 4, -1, 4, 3,
            -7, 8, -7, 5, -3, -2, -4, 9, 2, -1, 1, -10, -3, 5, -4,
            6, -8, -8, 9, -3, -5, 10, 3, -3, -3, 9, 3, -3, 2, -8,
            10, 1, 9, -2, -2, -3, -4, 6, -10, -1, 8, -8, 7, 3, -2,
            3, 6, -2, -2, -4, 1, -5, -4, 0, 5, 1, 9, -8, -2, -1
        ], [4, 5, 2, 3]);
        const b = tf.tensor4d([
            -4, -3, -2, -6, 6, -1, -4, -1, 7, -4, 8, -9, -9, 0, -1,
            -4, -6, -7, -3, -4, -7, 6, -8, 1, -2, 1, -1, -3, 8, -5,
            9, -2, 5, 9, -2, 2, -5, -5, -8, -1, -2, -3, -2, -10, 6,
            -3, 0, 1, 6, 7, 1, 2, -4, -5, 2, -5, -7, 9, 3, -6,
            6, 4, -4, 6, 10, -3, -2, 8, 10, -8, 10, -1, -9, -7, -8,
            -3, 1, 1, -2, -9, -7, -6, -1, 0, 7, -9, -7, -5, 0, -4,
            -4, -7, 2, 4, 6, 6, -4, -6, -8, 3, -8, -9, 6, 9, -4,
            1, -1, 0, 8, 9, 0, -5, 3, -1, 5, 0, -10, 7, -2, 6
        ], [4, 5, 3, 2]);
        const dy = tf.tensor4d([
            8, -7, 0, -9, -5, -5, 0, 3, 7, -4, 6, -8, -8, 0, -1, -8,
            -9, -7, -4, -9, 2, 3, 5, 8, -5, -7, 3, -10, -5, -9, -5, 1,
            7, 1, -9, -10, 8, 5, 0, 8, -6, 4, 0, -5, 8, -7, -2, 1,
            -8, 9, 9, -7, 1, 7, -2, 5, -2, 9, 1, -5, 7, 5, -7, -6,
            6, 7, -8, 7, 4, -5, 4, -5, 3, -4, -5, 4, -6, 3, -8, 10
        ], [4, 5, 2, 2]);
        const grads = tf.grads((a, b) => tf.matMul(a, b, false, false));
        const [da, db] = grads([a, b], dy);
        // da = dy * bT
        expect(da.shape).toEqual(a.shape);
        expectArraysClose(await da.data(), [
            -11, 26, 55, 27, 54, 9, 25, -15, 5, -3, -12, -27, -63, 9,
            -14, -54, 26, 20, 24, 56, 64, 35, -41, 0, 11, 30, -37, -1,
            31, 13, 12, 37, 2, 29, 97, 6, 60, 47, 31, 35, -14, 24,
            100, -3, -9, 0, -33, 1, 49, 9, -33, -124, -29, 86, -9, -11,
            -6, -40, 72, -48, -20, 48, -72, -20, -30, 15, -72, 136, 87, 12,
            -28, -21, 9, 37, 1, -32, -51, 2, -65, -49, -1, -41, -16, 2,
            -95, -31, -36, 52, 18, 20, -63, 34, 72, 70, -38, -78, -66, -27,
            -111, -10, 85, 1, -21, -21, -4, -21, -21, -4, -12, 20, 13, -4,
            -20, -19, -30, 81, 30, -40, 150, 76
        ]);
        // db = aT * dy
        expect(db.shape).toEqual(b.shape);
        expectArraysClose(await db.data(), [
            -16, 59, 24, -48, 40, -116, 15, 18, 25, -2, -5, 22, -84, 80,
            36, -16, -38, 8, -74, -16, 46, -80, 62, 48, 96, 110, 38, 6,
            -77, -54, 58, 91, -57, -90, 45, 70, 46, 36, 20, 99, -3, 10,
            55, 79, -10, 42, 5, -31, 85, 47, -74, -89, 37, 75, -48, -38,
            -64, -8, 32, 44, 42, -53, -48, 47, 42, -18, -30, 27, 70, -62,
            36, -24, 78, -69, -112, 101, -40, 20, -11, 113, -9, -6, 1, -50,
            3, -12, -16, 71, -14, 67, 84, 62, 21, 17, 84, 63, -16, -35,
            -28, 98, 4, -126, 40, -50, 36, -45, -16, 20, 19, -12, 8, 0,
            3, -4, 34, -65, 10, -17, -46, 17
        ]);
    });
    it('gradients: A x B^t', async () => {
        const a = tf.tensor3d([
            -5, -5, -6, 8, -2, -8, 4, -7, -6, -9, -1, 3, 7, -2, 5,
            -6, 3, 8, 7, -8, 1, 4, -4, 6, 4, -4, -9, -5, 2, -2
        ], [5, 3, 2]);
        const b = tf.tensor3d([
            -8, -4, -1, 0, -7, 0, 3, 3, 6, 2, -1, 8, -4, 9, -6,
            5, 8, 9, -9, 7, 0, -1, -1, -10, -7, 3, 4, 6, 3, -4
        ], [5, 3, 2]);
        const dy = tf.tensor3d([
            -0, 7, 5, 0, -9, 5, -7, 6, -5, -3, -2, -2, -4, 10, -3,
            5, -1, 3, -2, -9, 4, -5, 7, 9, -10, -8, -8, -5, -0, -1,
            3, 3, 4, 9, -7, 6, -2, -9, 5, 1, -5, -3, -1, 9, 4
        ], [5, 3, 3]);
        const grads = tf.grads((a, b) => tf.matMul(a, b, false, true));
        const [da, db] = grads([a, b], dy);
        expect(da.shape).toEqual(a.shape);
        expectArraysClose(await da.data(), [
            -42, 0, -26, 0, 85, 28, -19, -29, 51, -16, 6, 37, 94, -27, 50,
            71, 24, -202, 46, -25, -31, -22, -87, 10, -7, -80, -36, -15, 55, 35
        ]);
        expect(db.shape).toEqual(b.shape);
        expectArraysClose(await db.data(), [
            14, 56, 7, -155, -45, 55, 7, 72, -67, -79, 7, 50, -69, -46, -52,
            -88, 49, -126, -68, 106, 31, -30, -27, 60, -19, 5, 27, 43, 55, -13
        ]);
    });
    it('4d gradients: A x B^t', async () => {
        const a = tf.tensor4d([
            -2, 3, 5, -5, 3, 9, -3, -5, 1, 1, -9, 9, -6, 6, -8,
            -7, -1, 3, 9, -7, -7, 2, 10, -6, -8, -6, 9, -6, 4, -1,
            9, -6, 10, 8, -9, 5, -8, -7, 0, 2, -5, -1, -9, -4, 3,
            -2, 6, -4, 7, 1, -5, -4, 9, -8, -6, -8, 4, -1, 4, 3,
            -7, 8, -7, 5, -3, -2, -4, 9, 2, -1, 1, -10, -3, 5, -4,
            6, -8, -8, 9, -3, -5, 10, 3, -3, -3, 9, 3, -3, 2, -8,
            10, 1, 9, -2, -2, -3, -4, 6, -10, -1, 8, -8, 7, 3, -2,
            3, 6, -2, -2, -4, 1, -5, -4, 0, 5, 1, 9, -8, -2, -1
        ], [4, 5, 3, 2]);
        const b = tf.tensor4d([
            -4, -3, -2, -6, 6, -1, -4, -1, 7, -4, 8, -9, -9, 0, -1,
            -4, -6, -7, -3, -4, -7, 6, -8, 1, -2, 1, -1, -3, 8, -5,
            9, -2, 5, 9, -2, 2, -5, -5, -8, -1, -2, -3, -2, -10, 6,
            -3, 0, 1, 6, 7, 1, 2, -4, -5, 2, -5, -7, 9, 3, -6,
            6, 4, -4, 6, 10, -3, -2, 8, 10, -8, 10, -1, -9, -7, -8,
            -3, 1, 1, -2, -9, -7, -6, -1, 0, 7, -9, -7, -5, 0, -4,
            -4, -7, 2, 4, 6, 6, -4, -6, -8, 3, -8, -9, 6, 9, -4,
            1, -1, 0, 8, 9, 0, -5, 3, -1, 5, 0, -10, 7, -2, 6
        ], [4, 5, 3, 2]);
        const dy = tf.tensor4d([
            5, -1, -5, -4, -1, 9, 1, -2, 10, 7, -1, 6, -8, 8, -3,
            9, -4, 2, -4, -8, 8, 4, 8, -10, -8, -8, 6, 6, -5, 9,
            -1, -7, -5, -3, -3, 2, -6, 5, 8, -9, 5, -8, -3, 8, 6,
            2, 8, 5, 9, 7, 6, 2, -3, 10, 7, 7, -3, 4, -3, -6,
            -8, -8, 9, 0, -8, -3, -2, -2, 8, 2, 3, -6, 3, 6, -3,
            7, 7, -9, -3, 8, 7, 7, -1, -6, 5, 2, -1, -1, 1, 5,
            0, -4, 3, -4, -10, 1, -2, -8, -9, -6, 4, 4, -7, -1, -1,
            -9, 7, 1, -1, 8, 0, -2, -7, 5, 7, 8, 9, -3, -8, -6,
            -7, -8, -1, 8, -4, 7, 5, -9, 9, 3, 0, -10, 7, -9, 4,
            -7, 5, -2, -2, 3, 3, -6, 2, 0, 8, -5, -10, 3, -7, 0,
            -6, 2, 3, -1, 3, 3, -10, 1, 3, -7, -1, 8, -2, -1, -1,
            -3, -9, 7, 4, -6, 3, 0, -7, -4, -5, -8, -6, 10, -6, 4
        ], [4, 5, 3, 3]);
        const grads = tf.grads((a, b) => tf.matMul(a, b, false, true));
        const [da, db] = grads([a, b], dy);
        expect(da.shape).toEqual(a.shape);
        expectArraysClose(await da.data(), [
            -48, -4, 72, 9, 60, -1, 13, -57, 64, 3, -48, -11, -4, -24,
            16, 38, 44, -10, -55, -45, 92, -43, 14, -4, 71, -61, -51, 16,
            46, -57, 48, 78, 104, 57, -17, -11, -85, -33, 16, 1, 86, 21,
            -48, 21, -8, 34, 14, -35, 36, 48, 85, 108, -38, -40, 3, -8,
            -7, -1, 6, -16, 46, -33, 26, -79, -70, -29, 92, -84, -6, -47,
            98, -129, -55, -17, 79, 40, -118, -64, 68, 75, 71, 111, 5, -48,
            98, -36, 21, 13, 112, -34, 26, 57, 32, 44, 28, 50, 88, 27,
            44, -39, -16, 15, -21, -6, -67, -89, -46, -64, -19, -12, -3, 11,
            41, 63, 78, -73, 67, -92, 102, -18
        ]);
        expect(db.shape).toEqual(b.shape);
        expectArraysClose(await db.data(), [
            -27, 44, -9, -16, 85, 30, -110, 38, 47, -23, -39, -15, 0, -76,
            -8, -128, 26, 136, 31, -26, -26, 39, 136, -85, -45, 93, 37, -68,
            -112, -6, 90, 70, 169, -7, 15, 68, -16, -33, -16, -47, -21, 0,
            6, -4, 84, 24, 15, 20, -41, -1, 79, -86, 87, -23, -26, -64,
            18, 9, 52, 64, 34, -16, 122, -66, -1, 47, 1, 43, -11, -33,
            -17, 27, -45, -73, -60, -66, -92, -42, 32, -85, -44, -44, -28, -13,
            8, -20, 9, -9, -49, 79, -76, 15, 73, -7, 7, -8, -110, 93,
            106, -39, 64, -84, -29, -19, 13, 14, 63, 2, -15, 23, 17, 49,
            -3, -31, -65, 30, -95, 63, -82, 40
        ]);
    });
    it('gradients: A^t x B', async () => {
        const a = tf.tensor3d([
            -5, -5, -6, 8, -2, -8, 4, -7, -6, -9, -1, 3, 7, -2, 5,
            -6, 3, 8, 7, -8, 1, 4, -4, 6, 4, -4, -9, -5, 2, -2
        ], [5, 3, 2]);
        const b = tf.tensor3d([
            -8, -4, -1, 0, -7, 0, 3, 3, 6, 2, -1, 8, -4, 9, -6,
            5, 8, 9, -9, 7, 0, -1, -1, -10, -7, 3, 4, 6, 3, -4
        ], [5, 3, 2]);
        const dy = tf.tensor3d([8, 2, -3, -2, -8, 4, 5, 7, 4, -4, -4, 5, 8, 10, 1, 0, 6, 6, -4, 7], [5, 2, 2]);
        const grads = tf.grads((a, b) => tf.matMul(a, b, true, false));
        const [da, db] = grads([a, b], dy);
        expect(da.shape).toEqual(a.shape);
        expectArraysClose(await da.data(), [
            -72, 32, -8, 3, -56, 21, -12, 36, -40, 44, 40, 51, -52, 61, -44,
            49, -4, 13, -2, -9, -10, 0, -108, -1, -24, 49, 60, 26, -6, -40
        ]);
        expect(db.shape).toEqual(b.shape);
        expectArraysClose(await db.data(), [
            -25, 0, -72, -28, 8, 12, -67, -33, 3, -87, 23, 17, 36, -38, 44,
            -50, -20, 28, 48, 70, 12, 10, -26, -40, 40, -4, -34, -89, 20, -2
        ]);
    });
    it('gradients: A^t x B^t', async () => {
        const a = tf.tensor3d([
            -5, -5, -6, 8, -2, -8, 4, -7, -6, -9, -1, 3, 7, -2, 5,
            -6, 3, 8, 7, -8, 1, 4, -4, 6, 4, -4, -9, -5, 2, -2
        ], [5, 3, 2]);
        const b = tf.tensor3d([
            -8, -4, -1, 0, -7, 0, 3, 3, 6, 2, -1, 8, -4, 9, -6,
            5, 8, 9, -9, 7, 0, -1, -1, -10, -7, 3, 4, 6, 3, -4
        ], [5, 2, 3]);
        const dy = tf.tensor3d([8, 2, -3, -2, -8, 4, 5, 7, 4, -4, -4, 5, 8, 10, 1, 0, 6, 6, -4, 7], [5, 2, 2]);
        const grads = tf.grads((a, b) => tf.matMul(a, b, true, true));
        const [da, db] = grads([a, b], dy);
        expect(da.shape).toEqual(a.shape);
        expectArraysClose(await da.data(), [
            -64, 24, -46, 26, -8, 3, -16, 29, -28, 8, -16, 86, -36, 41, 4,
            4, -60, 69, -82, -9, 46, 7, -100, 0, -6, 70, 36, 9, 0, -44
        ]);
        expect(db.shape).toEqual(b.shape);
        expectArraysClose(await db.data(), [
            -25, -72, 8, 0, -28, 12, -67, 3, 23, -33, -87, 17, 36, 44, -20,
            -38, -50, 28, 48, 12, -26, 70, 10, -40, 40, -34, 20, -4, -89, -2
        ]);
    });
});
describeWithFlags('dot', ALL_ENVS, () => {
    let a;
    let b;
    let c;
    let d;
    let e;
    let f;
    beforeEach(() => {
        a = tf.tensor1d([1, 2]);
        b = tf.tensor2d([[1, 2], [3, 4]]);
        c = tf.tensor2d([[1, 2, 3], [4, 5, 6]]);
        d = tf.tensor3d([1, 2], [1, 1, 2]);
        e = tf.scalar(1);
        f = tf.tensor3d([1, 2, 1, 2], [2, 1, 2]);
    });
    it('vector-vector', async () => {
        const aa = tf.dot(a, a);
        expectArraysClose(await aa.data(), [5]);
        expect(aa.shape).toEqual([]);
    });
    it('vector-matrix', async () => {
        const ab = tf.dot(a, b);
        const ac = tf.dot(a, c);
        expect(ab.shape).toEqual([2]);
        expect(ac.shape).toEqual([3]);
        expectArraysClose(await ab.data(), [7, 10]);
        expectArraysClose(await ac.data(), [9, 12, 15]);
    });
    it('matrix-vector', async () => {
        const ba = b.dot(a);
        expect(ba.shape).toEqual([2]);
        expectArraysClose(await ba.data(), [5, 11]);
    });
    it('matrix-matrix', async () => {
        const bb = tf.dot(b, b);
        const bc = tf.dot(b, c);
        expect(bb.shape).toEqual([2, 2]);
        expect(bc.shape).toEqual([2, 3]);
        expectArraysClose(await bb.data(), [7, 10, 15, 22]);
        expectArraysClose(await bc.data(), [9, 12, 15, 19, 26, 33]);
    });
    it('matmul A x B asymmetric', async () => {
        const a = tf.tensor2d([1, 2, 3, 4], [2, 2]);
        const b = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const c = tf.matMul(a, b);
        const cData = await c.data();
        expect(c.shape).toEqual([2, 3]);
        expectArraysClose(cData, [9, 12, 15, 19, 26, 33]);
    });
    it('broadcast batch shape', async () => {
        const a = tf.tensor3d([1, 2, 3, 4], [1, 2, 2]);
        const b = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const c = tf.matMul(a, b);
        const cData = await c.data();
        expect(c.shape).toEqual([1, 2, 3]);
        expectArraysClose(cData, [9, 12, 15, 19, 26, 33]);
    });
    it('throws error on incompatible dimensions', () => {
        expect(() => tf.dot(c, f)).toThrowError();
    });
    it('throws error when inputs are not rank 1 or 2', () => {
        expect(() => tf.dot(a, d)).toThrowError();
        expect(() => tf.dot(a, e)).toThrowError();
    });
    it('accepts a tensor-like object', async () => {
        const a = [1, 2, 3];
        const res = tf.dot(a, a);
        expectArraysClose(await res.data(), [14]);
        expect(res.shape).toEqual([]);
    });
    it('throws error for string tensors', () => {
        expect(() => tf.dot('a', 'b'))
            .toThrowError(/Argument 't1' passed to 'dot' must be numeric tensor/);
    });
    it('ensure no memory leak', async () => {
        const numTensorsBefore = tf.memory().numTensors;
        const numDataIdBefore = tf.engine().backend.numDataIds();
        const a = tf.tensor2d([1, 2, 3, 4, 5, 6], [2, 3]);
        const b = tf.tensor2d([0, 1, -3, 2, 2, 1], [3, 2]);
        const c = tf.matMul(a, b);
        expect(c.shape).toEqual([2, 2]);
        expectArraysClose(await c.data(), [0, 8, -3, 20]);
        a.dispose();
        b.dispose();
        c.dispose();
        const numTensorsAfter = tf.memory().numTensors;
        const numDataIdAfter = tf.engine().backend.numDataIds();
        expect(numTensorsAfter).toBe(numTensorsBefore);
        expect(numDataIdAfter).toBe(numDataIdBefore);
    });
});
//# sourceMappingURL=data:application/json;base64,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