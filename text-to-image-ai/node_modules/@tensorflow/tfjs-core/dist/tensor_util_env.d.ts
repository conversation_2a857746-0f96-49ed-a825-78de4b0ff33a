/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-core/dist/tensor_util_env" />
import { Tensor } from './tensor';
import { DataType, TensorLike, WebGLData, WebGPUData } from './types';
export declare function inferShape(val: TensorLike | WebGLData | WebGPUData, dtype?: DataType): number[];
export declare function convertToTensor<T extends Tensor>(x: T | TensorLike, argName: string, functionName: string, parseAsDtype?: DataType | 'numeric' | 'string_or_numeric'): T;
export declare function convertToTensorArray<T extends Tensor>(arg: Array<T | TensorLike>, argName: string, functionName: string, parseAsDtype?: DataType | 'numeric' | 'string_or_numeric'): T[];
