#!/usr/bin/env node

/**
 * @license
 * Copyright 2020 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

import * as chalk from 'chalk';

/**
 * Normalized kernels names to the variable name used in code for the kernel
 * config.
 */
export function kernelNameToVariableName(kernelName: string) {
  if (kernelName.startsWith('_')) {
    // e.g. _FusedMatMulConfig
    return `_${kernelName.charAt(1).toLowerCase()}${kernelName.slice(2)}`;
  }
  return kernelName.charAt(0).toLowerCase() + kernelName.slice(1);
}

/**
 * Given an op name returns the name of the file that would export that op.
 */
export function opNameToFileName(opName: string) {
  // add exceptions here.
  if (opName === 'isNaN') {
    return 'is_nan';
  } else if (opName.match(/(.*)ND/)) {
    return opName.match(/(.*)ND/)[1].replace(
               /[A-Z]/g, (s: string) => `_${s.toLowerCase()}`) +
        '_nd';
  } else if (opName === 'avgPool3d') {
    return 'avg_pool_3d';
  } else if (opName.match(/concat[0-9]d/)) {
    return `concat_${opName.match(/concat([0-9]d)/)[1]}`;
  }
  return opName.replace(/[A-Z]/g, (s: string) => `_${s.toLowerCase()}`);
}

export function getPreamble() {
  const preamble = `/**
 * @license
 * Copyright ${(new Date()).getFullYear()} Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

// This file is autogenerated.\n
`;
  return preamble;
}

export function bail(errorMsg: string) {
  console.log(chalk.red(errorMsg));
  process.exit(1);
}
