import {UnitModel} from '../unit';
import {MarkCompiler} from './base';
import * as encode from './encode';

export const rect: MarkCompiler = {
  vgMark: 'rect',
  encodeEntry: (model: UnitModel) => {
    return {
      ...encode.baseEncodeEntry(model, {
        align: 'ignore',
        baseline: 'ignore',
        color: 'include',
        orient: 'ignore',
        size: 'ignore',
        theta: 'ignore'
      }),
      ...encode.rectPosition(model, 'x', 'rect'),
      ...encode.rectPosition(model, 'y', 'rect')
    };
  }
};
