import { ScaleType, SignalRef } from 'vega';
import { AxisConfig } from '../../axis';
import { PositionScaleChannel } from '../../channel';
import { Config } from '../../config';
export declare type AxisConfigs = ReturnType<typeof getAxisConfigs>;
export declare function getAxisConfigs(channel: PositionScaleChannel, scaleType: ScaleType, orient: string | SignalRef, config: Config): {
    vlOnlyAxisConfig: any;
    vgAxisConfig: any;
    axisConfigStyle: any;
};
export declare function getAxisConfigStyle(axisConfigTypes: string[], config: Config): any;
export declare function getAxisConfig(property: keyof AxisConfig, config: Config, style: string | string[], axisConfigs?: Partial<AxisConfigs>): {
    configFrom?: string;
    configValue?: any;
};
//# sourceMappingURL=config.d.ts.map