{"name": "text-to-image-ai", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "text-to-image-ai", "version": "1.0.0", "license": "MIT", "dependencies": {"@tensorflow/tfjs": "^4.15.0", "@tensorflow/tfjs-vis": "^1.5.1"}, "devDependencies": {"http-server": "^14.1.1"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@tensorflow/tfjs": {"version": "4.22.0", "resolved": "https://registry.npmmirror.com/@tensorflow/tfjs/-/tfjs-4.22.0.tgz", "integrity": "sha512-0TrIrXs6/b7FLhLVNmfh8Sah6JgjBPH4mZ8JGb7NU6WW+cx00qK5BcAZxw7NCzxj6N8MRAIfHq+oNbPUNG5VAg==", "license": "Apache-2.0", "dependencies": {"@tensorflow/tfjs-backend-cpu": "4.22.0", "@tensorflow/tfjs-backend-webgl": "4.22.0", "@tensorflow/tfjs-converter": "4.22.0", "@tensorflow/tfjs-core": "4.22.0", "@tensorflow/tfjs-data": "4.22.0", "@tensorflow/tfjs-layers": "4.22.0", "argparse": "^1.0.10", "chalk": "^4.1.0", "core-js": "3.29.1", "regenerator-runtime": "^0.13.5", "yargs": "^16.0.3"}, "bin": {"tfjs-custom-module": "dist/tools/custom_module/cli.js"}}, "node_modules/@tensorflow/tfjs-backend-cpu": {"version": "4.22.0", "resolved": "https://registry.npmmirror.com/@tensorflow/tfjs-backend-cpu/-/tfjs-backend-cpu-4.22.0.tgz", "integrity": "sha512-1u0FmuLGuRAi8D2c3cocHTASGXOmHc/4OvoVDENJayjYkS119fcTcQf4iHrtLthWyDIPy3JiPhRrZQC9EwnhLw==", "license": "Apache-2.0", "dependencies": {"@types/seedrandom": "^2.4.28", "seedrandom": "^3.0.5"}, "engines": {"yarn": ">= 1.3.2"}, "peerDependencies": {"@tensorflow/tfjs-core": "4.22.0"}}, "node_modules/@tensorflow/tfjs-backend-webgl": {"version": "4.22.0", "resolved": "https://registry.npmmirror.com/@tensorflow/tfjs-backend-webgl/-/tfjs-backend-webgl-4.22.0.tgz", "integrity": "sha512-H535XtZWnWgNwSzv538czjVlbJebDl5QTMOth4RXr2p/kJ1qSIXE0vZvEtO+5EC9b00SvhplECny2yDewQb/Yg==", "license": "Apache-2.0", "dependencies": {"@tensorflow/tfjs-backend-cpu": "4.22.0", "@types/offscreencanvas": "~2019.3.0", "@types/seedrandom": "^2.4.28", "seedrandom": "^3.0.5"}, "engines": {"yarn": ">= 1.3.2"}, "peerDependencies": {"@tensorflow/tfjs-core": "4.22.0"}}, "node_modules/@tensorflow/tfjs-converter": {"version": "4.22.0", "resolved": "https://registry.npmmirror.com/@tensorflow/tfjs-converter/-/tfjs-converter-4.22.0.tgz", "integrity": "sha512-PT43MGlnzIo+YfbsjM79Lxk9lOq6uUwZuCc8rrp0hfpLjF6Jv8jS84u2jFb+WpUeuF4K33ZDNx8CjiYrGQ2trQ==", "license": "Apache-2.0", "peerDependencies": {"@tensorflow/tfjs-core": "4.22.0"}}, "node_modules/@tensorflow/tfjs-core": {"version": "4.22.0", "resolved": "https://registry.npmmirror.com/@tensorflow/tfjs-core/-/tfjs-core-4.22.0.tgz", "integrity": "sha512-LEkOyzbknKFoWUwfkr59vSB68DMJ4cjwwHgicXN0DUi3a0Vh1Er3JQqCI1Hl86GGZQvY8ezVrtDIvqR1ZFW55A==", "license": "Apache-2.0", "dependencies": {"@types/long": "^4.0.1", "@types/offscreencanvas": "~2019.7.0", "@types/seedrandom": "^2.4.28", "@webgpu/types": "0.1.38", "long": "4.0.0", "node-fetch": "~2.6.1", "seedrandom": "^3.0.5"}, "engines": {"yarn": ">= 1.3.2"}}, "node_modules/@tensorflow/tfjs-core/node_modules/@types/offscreencanvas": {"version": "2019.7.3", "resolved": "https://registry.npmmirror.com/@types/offscreencanvas/-/offscreencanvas-2019.7.3.tgz", "integrity": "sha512-ieXiYmgSRXUDeOntE1InxjWyvEelZGP63M+cGuquuRLuIKKT1osnkXjxev9B7d1nXSug5vpunx+gNlbVxMlC9A==", "license": "MIT"}, "node_modules/@tensorflow/tfjs-data": {"version": "4.22.0", "resolved": "https://registry.npmmirror.com/@tensorflow/tfjs-data/-/tfjs-data-4.22.0.tgz", "integrity": "sha512-dYmF3LihQIGvtgJrt382hSRH4S0QuAp2w1hXJI2+kOaEqo5HnUPG0k5KA6va+S1yUhx7UBToUKCBHeLHFQRV4w==", "license": "Apache-2.0", "dependencies": {"@types/node-fetch": "^2.1.2", "node-fetch": "~2.6.1", "string_decoder": "^1.3.0"}, "peerDependencies": {"@tensorflow/tfjs-core": "4.22.0", "seedrandom": "^3.0.5"}}, "node_modules/@tensorflow/tfjs-layers": {"version": "4.22.0", "resolved": "https://registry.npmmirror.com/@tensorflow/tfjs-layers/-/tfjs-layers-4.22.0.tgz", "integrity": "sha512-lybPj4ZNj9iIAPUj7a8ZW1hg8KQGfqWLlCZDi9eM/oNKCCAgchiyzx8OrYoWmRrB+AM6VNEeIT+2gZKg5ReihA==", "license": "Apache-2.0 AND MIT", "peerDependencies": {"@tensorflow/tfjs-core": "4.22.0"}}, "node_modules/@tensorflow/tfjs-vis": {"version": "1.5.1", "resolved": "https://registry.npmmirror.com/@tensorflow/tfjs-vis/-/tfjs-vis-1.5.1.tgz", "integrity": "sha512-oNithKiR7VZaE+xUvz6Leww4TYEPhKi8j5xnEYvT3j7brK2Njdvril7UgFtZ8EYZBdeX6XNim5Eu3/23gTQ1dA==", "license": "Apache-2.0", "dependencies": {"d3-format": "~1.3.0", "d3-selection": "~1.3.0", "glamor": "~2.20.40", "preact": "~8.2.9", "vega": "5.20.0", "vega-embed": "6.17.0", "vega-lite": "4.13.1"}, "peerDependencies": {"@tensorflow/tfjs-core": ">= 1.0.0"}}, "node_modules/@types/clone": {"version": "0.1.30", "resolved": "https://registry.npmmirror.com/@types/clone/-/clone-0.1.30.tgz", "integrity": "sha512-vcxBr+ybljeSiasmdke1cQ9ICxoEwaBgM1OQ/P5h4MPj/kRyLcDl5L8PrftlbyV1kBbJIs3M3x1A1+rcWd4mEA==", "license": "MIT"}, "node_modules/@types/estree": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/@types/estree/-/estree-1.0.8.tgz", "integrity": "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==", "license": "MIT"}, "node_modules/@types/fast-json-stable-stringify": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/@types/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz", "integrity": "sha512-mky/O83TXmGY39P1H9YbUpjV6l6voRYlufqfFCvel8l1phuy8HRjdWc1rrPuN53ITBJlbyMSV6z3niOySO5pgQ==", "license": "MIT"}, "node_modules/@types/long": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/@types/long/-/long-4.0.2.tgz", "integrity": "sha512-MqTGEo5bj5t157U6fA/BiDynNkn0YknVdh48CMPkTSpFTVmvao5UQmm7uEF6xBEo7qIMAlY/JSleYaE6VOdpaA==", "license": "MIT"}, "node_modules/@types/node": {"version": "24.2.0", "resolved": "https://registry.npmmirror.com/@types/node/-/node-24.2.0.tgz", "integrity": "sha512-3xyG3pMCq3oYCNg7/ZP+E1ooTaGB4cG8JWRsqqOYQdbWNY4zbaV0Ennrd7stjiJEFZCaybcIgpTjJWHRfBSIDw==", "license": "MIT", "dependencies": {"undici-types": "~7.10.0"}}, "node_modules/@types/node-fetch": {"version": "2.6.13", "resolved": "https://registry.npmmirror.com/@types/node-fetch/-/node-fetch-2.6.13.tgz", "integrity": "sha512-QGpRVpzSaUs30JBSGPjOg4Uveu384erbHBoT1zeONvyCfwQxIkUshLAOqN/k9EjGviPRmWTTe6aH2qySWKTVSw==", "license": "MIT", "dependencies": {"@types/node": "*", "form-data": "^4.0.4"}}, "node_modules/@types/offscreencanvas": {"version": "2019.3.0", "resolved": "https://registry.npmmirror.com/@types/offscreencanvas/-/offscreencanvas-2019.3.0.tgz", "integrity": "sha512-esIJx9bQg+QYF0ra8GnvfianIY8qWB0GBx54PK5Eps6m+xTj86KLavHv6qDhzKcu5UUOgNfJ2pWaIIV7TRUd9Q==", "license": "MIT"}, "node_modules/@types/seedrandom": {"version": "2.4.34", "resolved": "https://registry.npmmirror.com/@types/seedrandom/-/seedrandom-2.4.34.tgz", "integrity": "sha512-ytDiArvrn/3Xk6/vtylys5tlY6eo7Ane0hvcx++TKo6RxQXuVfW0AF/oeWqAj9dN29SyhtawuXstgmPlwNcv/A==", "license": "MIT"}, "node_modules/@webgpu/types": {"version": "0.1.38", "resolved": "https://registry.npmmirror.com/@webgpu/types/-/types-0.1.38.tgz", "integrity": "sha512-7LrhVKz2PRh+DD7+S+PVaFd5HxaWQvoMqBbsV9fNJO1pjUs1P8bM2vQVNfk+3URTqbuTI7gkXi0rfsN0IadoBA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/argparse": {"version": "1.0.10", "resolved": "https://registry.npmmirror.com/argparse/-/argparse-1.0.10.tgz", "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/array-flat-polyfill": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/array-flat-polyfill/-/array-flat-polyfill-1.0.1.tgz", "integrity": "sha512-hfJmKupmQN0lwi0xG6FQ5U8Rd97RnIERplymOv/qpq8AoNKPPAnxJadjFA23FNWm88wykh9HmpLJUUwUtNU/iw==", "license": "CC0-1.0", "engines": {"node": ">=6.0.0"}}, "node_modules/asap": {"version": "2.0.6", "resolved": "https://registry.npmmirror.com/asap/-/asap-2.0.6.tgz", "integrity": "sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==", "license": "MIT"}, "node_modules/async": {"version": "3.2.6", "resolved": "https://registry.npmmirror.com/async/-/async-3.2.6.tgz", "integrity": "sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==", "dev": true, "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==", "license": "MIT"}, "node_modules/basic-auth": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/basic-auth/-/basic-auth-2.0.1.tgz", "integrity": "sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.8"}}, "node_modules/bowser": {"version": "1.9.4", "resolved": "https://registry.npmmirror.com/bowser/-/bowser-1.9.4.tgz", "integrity": "sha512-9IdMmj2KjigRq6oWhmwv1W36pDuA4STQZ8q6YO9um+x07xgYNCD3Oou+WP/3L1HNz7iqythGet3/p4wvc8AAwQ==", "license": "MIT"}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/camelcase": {"version": "5.3.1", "resolved": "https://registry.npmmirror.com/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/cliui": {"version": "7.0.4", "resolved": "https://registry.npmmirror.com/cliui/-/cliui-7.0.4.tgz", "integrity": "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/clone": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/clone/-/clone-2.1.2.tgz", "integrity": "sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.20.3", "resolved": "https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==", "license": "MIT"}, "node_modules/core-js": {"version": "3.29.1", "resolved": "https://registry.npmmirror.com/core-js/-/core-js-3.29.1.tgz", "integrity": "sha512-+jwgnhg6cQxKYIIjGtAHq2nwUOolo9eoFZ4sHfUH09BLXBgxnH4gA0zEd+t+BO2cNB8idaBtZFcFTRjQJRJmAw==", "hasInstallScript": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/corser": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/corser/-/corser-2.0.1.tgz", "integrity": "sha512-utCYNzRSQIZNPIcGZdQc92UVJYAhtGAteCFg0yRaFm8f0P+CPtyGyHXJcGXnffjCybUCEx3FQ2G7U3/o9eIkVQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/css-in-js-utils": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/css-in-js-utils/-/css-in-js-utils-2.0.1.tgz", "integrity": "sha512-PJF0SpJT+WdbVVt0AOYp9C8GnuruRlL/UFW7932nLWmFLQTaWEzTBQEx7/hn4BuV+WON75iAViSUJLiU3PKbpA==", "license": "MIT", "dependencies": {"hyphenate-style-name": "^1.0.2", "isobject": "^3.0.1"}}, "node_modules/d3-array": {"version": "2.12.1", "resolved": "https://registry.npmmirror.com/d3-array/-/d3-array-2.12.1.tgz", "integrity": "sha512-B0ErZK/66mHtEsR1TkPEEkwdy+WDesimkM5gpZr5Dsg54BiTA5RXtYW5qTLIAcekaS9xfZrzBLF/OAkB3Qn1YQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"internmap": "^1.0.0"}}, "node_modules/d3-color": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/d3-color/-/d3-color-2.0.0.tgz", "integrity": "sha512-SPXi0TSKPD4g9tw0NMZFnR95XVgUZiBH+uUTqQuDu1OsE2zomHU7ho0FISciaPvosimixwHFl3WHLGabv6dDgQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/d3-delaunay": {"version": "5.3.0", "resolved": "https://registry.npmmirror.com/d3-delaunay/-/d3-delaunay-5.3.0.tgz", "integrity": "sha512-amALSrOllWVLaHTnDLHwMIiz0d1bBu9gZXd1FiLfXf8sHcX9jrcj81TVZOqD4UX7MgBZZ07c8GxzEgBpJqc74w==", "license": "ISC", "dependencies": {"delaunator": "4"}}, "node_modules/d3-dispatch": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/d3-dispatch/-/d3-dispatch-2.0.0.tgz", "integrity": "sha512-S/m2VsXI7gAti2pBoLClFFTMOO1HTtT0j99AuXLoGFKO6deHDdnv6ZGTxSTTUTgO1zVcv82fCOtDjYK4EECmWA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/d3-dsv": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/d3-dsv/-/d3-dsv-2.0.0.tgz", "integrity": "sha512-E+Pn8UJYx9mViuIUkoc93gJGGYut6mSDKy2+XaPwccwkRGlR+LO97L2VCCRjQivTwLHkSnAJG7yo00BWY6QM+w==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"commander": "2", "iconv-lite": "0.4", "rw": "1"}, "bin": {"csv2json": "bin/dsv2json", "csv2tsv": "bin/dsv2dsv", "dsv2dsv": "bin/dsv2dsv", "dsv2json": "bin/dsv2json", "json2csv": "bin/json2dsv", "json2dsv": "bin/json2dsv", "json2tsv": "bin/json2dsv", "tsv2csv": "bin/dsv2dsv", "tsv2json": "bin/dsv2json"}}, "node_modules/d3-force": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/d3-force/-/d3-force-2.1.1.tgz", "integrity": "sha512-nAuHEzBqMvpFVMf9OX75d00OxvOXdxY+xECIXjW6Gv8BRrXu6gAWbv/9XKrvfJ5i5DCokDW7RYE50LRoK092ew==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-dispatch": "1 - 2", "d3-quadtree": "1 - 2", "d3-timer": "1 - 2"}}, "node_modules/d3-format": {"version": "1.3.2", "resolved": "https://registry.npmmirror.com/d3-format/-/d3-format-1.3.2.tgz", "integrity": "sha512-Z18Dprj96ExragQ0DeGi+SYPQ7pPfRMtUXtsg/ChVIKNBCzjO8XYJvRTC1usblx52lqge56V5ect+frYTQc8WQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/d3-geo": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/d3-geo/-/d3-geo-2.0.2.tgz", "integrity": "sha512-8pM1WGMLGFuhq9S+FpPURxic+gKzjluCD/CHTuUF3mXMeiCo0i6R0tO1s4+GArRFde96SLcW/kOFRjoAosPsFA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.5.0"}}, "node_modules/d3-geo-projection": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/d3-geo-projection/-/d3-geo-projection-3.0.0.tgz", "integrity": "sha512-1JE+filVbkEX2bT25dJdQ05iA4QHvUwev6o0nIQHOSrNlHCAKfVss/U10vEM3pA4j5v7uQoFdQ4KLbx9BlEbWA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"commander": "2", "d3-array": "1 - 2", "d3-geo": "1.12.0 - 2", "resolve": "^1.1.10"}, "bin": {"geo2svg": "bin/geo2svg", "geograticule": "bin/geograticule", "geoproject": "bin/geoproject", "geoquantize": "bin/geoquantize", "geostitch": "bin/geostitch"}}, "node_modules/d3-hierarchy": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/d3-hierarchy/-/d3-hierarchy-2.0.0.tgz", "integrity": "sha512-SwIdqM3HxQX2214EG9GTjgmCc/mbSx4mQBn+DuEETubhOw6/U3fmnji4uCVrmzOydMHSO1nZle5gh6HB/wdOzw==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/d3-interpolate": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/d3-interpolate/-/d3-interpolate-2.0.1.tgz", "integrity": "sha512-c5UhwwTs/yybcmTpAVqwSFl6vrQ8JZJoT5F7xNFK9pymv5C0Ymcc9/LIJHtYIggg/yS9YHw8i8O8tgb9pupjeQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-color": "1 - 2"}}, "node_modules/d3-path": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/d3-path/-/d3-path-2.0.0.tgz", "integrity": "sha512-ZwZQxKhBnv9yHaiWd6ZU4x5BtCQ7pXszEV9CU6kRgwIQVQGLMv1oiL4M+MK/n79sYzsj+gcgpPQSctJUsLN7fA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/d3-quadtree": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/d3-quadtree/-/d3-quadtree-2.0.0.tgz", "integrity": "sha512-b0Ed2t1UUalJpc3qXzKi+cPGxeXRr4KU9YSlocN74aTzp6R/Ud43t79yLLqxHRWZfsvWXmbDWPpoENK1K539xw==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/d3-scale": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/d3-scale/-/d3-scale-3.3.0.tgz", "integrity": "sha512-1JGp44NQCt5d1g+Yy+GeOnZP7xHo0ii8zsQp6PGzd+C1/dl0KGsp9A7Mxwp+1D1o4unbTTxVdU/ZOIEBoeZPbQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.3.0", "d3-format": "1 - 2", "d3-interpolate": "1.2.0 - 2", "d3-time": "^2.1.1", "d3-time-format": "2 - 3"}}, "node_modules/d3-scale-chromatic": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/d3-scale-chromatic/-/d3-scale-chromatic-3.1.0.tgz", "integrity": "sha512-A3s5PWiZ9YCXFye1o246KoscMWqf8BsD9eRiJ3He7C9OBaxKhAd5TFCdEx/7VbKtxxTsu//1mMJFrEt572cEyQ==", "license": "ISC", "dependencies": {"d3-color": "1 - 3", "d3-interpolate": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-selection": {"version": "1.3.2", "resolved": "https://registry.npmmirror.com/d3-selection/-/d3-selection-1.3.2.tgz", "integrity": "sha512-OoXdv1nZ7h2aKMVg3kaUFbLLK5jXUFAMLD/Tu5JA96mjf8f2a9ZUESGY+C36t8R1WFeWk/e55hy54Ml2I62CRQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/d3-shape": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/d3-shape/-/d3-shape-2.1.0.tgz", "integrity": "sha512-PnjUqfM2PpskbSLTJvAzp2Wv4CZsnAgTfcVRTwW03QR3MkXF8Uo7B1y/lWkAsmbKwuecto++4NlsYcvYpXpTHA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-path": "1 - 2"}}, "node_modules/d3-time": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/d3-time/-/d3-time-2.1.1.tgz", "integrity": "sha512-/eIQe/eR4kCQwq7yxi7z4c6qEXf2IYGcjoWB5OOQy4Tq9Uv39/947qlDcN2TLkiTzQWzvnsuYPB9TrWaNfipKQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "2"}}, "node_modules/d3-time-format": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/d3-time-format/-/d3-time-format-3.0.0.tgz", "integrity": "sha512-UXJh6EKsHBTjopVqZBhFysQcoXSv/5yLONZvkQ5Kk3qbwiUYkdX17Xa1PT6U1ZWXGGfB1ey5L8dKMlFq2DO0Ag==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-time": "1 - 2"}}, "node_modules/d3-timer": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/d3-timer/-/d3-timer-2.0.0.tgz", "integrity": "sha512-TO4VLh0/420Y/9dO3+f9abDEFYeCUr2WZRlxJvbp4HPTQcSylXNiL6yZa9FIUvV1yRiFufl1bszTCLDqv9PWNA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decamelize": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/delaunator": {"version": "4.0.1", "resolved": "https://registry.npmmirror.com/delaunator/-/delaunator-4.0.1.tgz", "integrity": "sha512-WNPWi1IRKZfCt/qIDMfERkDp93+iZEmOxN2yy4Jg+Xhv8SLk2UTqqbe1sfiipn0and9QrE914/ihdx82Y/Giag==", "license": "ISC"}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "license": "MIT"}, "node_modules/encoding": {"version": "0.1.13", "resolved": "https://registry.npmmirror.com/encoding/-/encoding-0.1.13.tgz", "integrity": "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==", "license": "MIT", "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/encoding/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmmirror.com/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==", "dev": true, "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "license": "MIT"}, "node_modules/fast-json-patch": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/fast-json-patch/-/fast-json-patch-3.1.1.tgz", "integrity": "sha512-vf6IHUX2SBcA+5/+4883dsIjpBTqmfBjmYiWK1savxQmFk4JfBMLa7ynTYOs1Rolp/T1betJxHiGD3g1Mn8lUQ==", "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "license": "MIT"}, "node_modules/fbjs": {"version": "0.8.18", "resolved": "https://registry.npmmirror.com/fbjs/-/fbjs-0.8.18.tgz", "integrity": "sha512-EQaWFK+fEPSoibjNy8IxUtaFOMXcWsY0JaVrQoZR9zC8N2Ygf9iDITPWjUTVIax95b6I742JFLqASHfsag/vKA==", "license": "MIT", "dependencies": {"core-js": "^1.0.0", "isomorphic-fetch": "^2.1.1", "loose-envify": "^1.0.0", "object-assign": "^4.1.0", "promise": "^7.1.1", "setimmediate": "^1.0.5", "ua-parser-js": "^0.7.30"}}, "node_modules/fbjs/node_modules/core-js": {"version": "1.2.7", "resolved": "https://registry.npmmirror.com/core-js/-/core-js-1.2.7.tgz", "integrity": "sha512-ZiPp9pZlgxpWRu0M+YWbm6+aQ84XEfH1JRXvfOc/fILWI0VKhLC2LX13X1NYq4fULzLMq7Hfh43CSo2/aIaUPA==", "deprecated": "core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.", "license": "MIT"}, "node_modules/find-up": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz", "integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/follow-redirects": {"version": "1.15.11", "resolved": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.11.tgz", "integrity": "sha512-deG2P0JfjrTxl50XGCDyfI97ZGVCxIpfKYmfyrQ54n5FO/0gfIES8C/Psl6kWVDolizcaaxZJnTS0QSMxvnsBQ==", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data": {"version": "4.0.4", "resolved": "https://registry.npmmirror.com/form-data/-/form-data-4.0.4.tgz", "integrity": "sha512-KrGhL9Q4zjj0kiUt5OO4Mr/A/jlI2jDYs5eHBpYHPcBEVSiipAvn2Ko2HnPe20rmcuuvMHNdZFp+4IlGTMF0Ow==", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/glamor": {"version": "2.20.40", "resolved": "https://registry.npmmirror.com/glamor/-/glamor-2.20.40.tgz", "integrity": "sha512-DNXCd+c14N9QF8aAKrfl4xakPk5FdcFwmH7sD0qnC0Pr7xoZ5W9yovhUrY/dJc3psfGGXC58vqQyRtuskyUJxA==", "license": "MIT", "dependencies": {"fbjs": "^0.8.12", "inline-style-prefixer": "^3.0.6", "object-assign": "^4.1.1", "prop-types": "^15.5.10", "through": "^2.3.8"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/he/-/he-1.2.0.tgz", "integrity": "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/html-encoding-sniffer": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz", "integrity": "sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==", "dev": true, "license": "MIT", "dependencies": {"whatwg-encoding": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/http-proxy": {"version": "1.18.1", "resolved": "https://registry.npmmirror.com/http-proxy/-/http-proxy-1.18.1.tgz", "integrity": "sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==", "dev": true, "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-server": {"version": "14.1.1", "resolved": "https://registry.npmmirror.com/http-server/-/http-server-14.1.1.tgz", "integrity": "sha512-+cbxadF40UXd9T01zUHgA+rlo2Bg1Srer4+B4NwIHdaGxAGGv59nYRnGGDJ9LBk7alpS0US+J+bLLdQOOkJq4A==", "dev": true, "license": "MIT", "dependencies": {"basic-auth": "^2.0.1", "chalk": "^4.1.2", "corser": "^2.0.1", "he": "^1.2.0", "html-encoding-sniffer": "^3.0.0", "http-proxy": "^1.18.1", "mime": "^1.6.0", "minimist": "^1.2.6", "opener": "^1.5.1", "portfinder": "^1.0.28", "secure-compare": "3.0.1", "union": "~0.5.0", "url-join": "^4.0.1"}, "bin": {"http-server": "bin/http-server"}, "engines": {"node": ">=12"}}, "node_modules/hyphenate-style-name": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/hyphenate-style-name/-/hyphenate-style-name-1.1.0.tgz", "integrity": "sha512-WDC/ui2VVRrz3jOVi+XtjqkDjiVjTtFaAGiW37k6b+ohyQ5wYDOGkvCZa8+H0nx3gyvv0+BST9xuOgIyGQ00gw==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/inline-style-prefixer": {"version": "3.0.8", "resolved": "https://registry.npmmirror.com/inline-style-prefixer/-/inline-style-prefixer-3.0.8.tgz", "integrity": "sha512-ne8XIyyqkRaNJ1JfL1NYzNdCNxq+MCBQhC8NgOQlzNm2vv3XxlP0VSLQUbSRCF6KPEoveCVEpayHoHzcMyZsMQ==", "license": "MIT", "dependencies": {"bowser": "^1.7.3", "css-in-js-utils": "^2.0.0"}}, "node_modules/internmap": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/internmap/-/internmap-1.0.1.tgz", "integrity": "sha512-lDB5YccMydFBtasVtxnZ3MRBHuaoE8GKsppq+EchKL2U4nK/DmEpPHNH8MZe5HkMtpSiTSOZwfN0tzYjO/lJEw==", "license": "ISC"}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-stream": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/isobject": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/isobject/-/isobject-3.0.1.tgz", "integrity": "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/isomorphic-fetch": {"version": "2.2.1", "resolved": "https://registry.npmmirror.com/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz", "integrity": "sha512-9c4TNAKYXM5PRyVcwUZrF3W09nQ+sO7+jydgs4ZGW9dhsLG2VOlISJABombdQqQRXCwuYG3sYV/puGf5rp0qmA==", "license": "MIT", "dependencies": {"node-fetch": "^1.0.1", "whatwg-fetch": ">=0.10.0"}}, "node_modules/isomorphic-fetch/node_modules/node-fetch": {"version": "1.7.3", "resolved": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.7.3.tgz", "integrity": "sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ==", "license": "MIT", "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "license": "MIT"}, "node_modules/json-stringify-pretty-compact": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/json-stringify-pretty-compact/-/json-stringify-pretty-compact-3.0.0.tgz", "integrity": "sha512-Rc2suX5meI0S3bfdZuA7JMFBGkJ875ApfVyq2WHELjBiiG22My/l7/8zPpH/CfFVQHuVLd8NLR0nv6vi0BYYKA==", "license": "MIT"}, "node_modules/locate-path": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/long": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/long/-/long-4.0.0.tgz", "integrity": "sha512-XsP+KhQif4bjX1kbuSiySJFNAehNxgLb6hPRGJ9QsUr8ajHkuXGdrHmFUTUUXhDwVX2R5bY4JNZEwbUiMhV+MA==", "license": "Apache-2.0"}, "node_modules/loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://registry.npmmirror.com/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dev": true, "license": "MIT"}, "node_modules/node-fetch": {"version": "2.6.13", "resolved": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.13.tgz", "integrity": "sha512-StxNAxh15zr77QvvkmveSQ8uCQ4+v5FkvNTj0OESmiHu+VRi/gXArXtkWMElOsOUNLtUEvI4yS+rdtOHZTwlQA==", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/opener": {"version": "1.5.2", "resolved": "https://registry.npmmirror.com/opener/-/opener-1.5.2.tgz", "integrity": "sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==", "dev": true, "license": "(WTFPL OR MIT)", "bin": {"opener": "bin/opener-bin.js"}}, "node_modules/p-limit": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/p-try/-/p-try-2.2.0.tgz", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "license": "MIT"}, "node_modules/portfinder": {"version": "1.0.37", "resolved": "https://registry.npmmirror.com/portfinder/-/portfinder-1.0.37.tgz", "integrity": "sha512-yuGIEjDAYnnOex9ddMnKZEMFE0CcGo6zbfzDklkmT1m5z734ss6JMzN9rNB3+RR7iS+F10D4/BVIaXOyh8PQKw==", "dev": true, "license": "MIT", "dependencies": {"async": "^3.2.6", "debug": "^4.3.6"}, "engines": {"node": ">= 10.12"}}, "node_modules/preact": {"version": "8.2.9", "resolved": "https://registry.npmmirror.com/preact/-/preact-8.2.9.tgz", "integrity": "sha512-ThuGXBmJS3VsT+jIP+eQufD3L8pRw/PY3FoCys6O9Pu6aF12Pn9zAJDX99TfwRAFOCEKm/P0lwiPTbqKMJp0fA==", "hasInstallScript": true, "license": "MIT"}, "node_modules/promise": {"version": "7.3.1", "resolved": "https://registry.npmmirror.com/promise/-/promise-7.3.1.tgz", "integrity": "sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==", "license": "MIT", "dependencies": {"asap": "~2.0.3"}}, "node_modules/prop-types": {"version": "15.8.1", "resolved": "https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz", "integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/qs": {"version": "6.14.0", "resolved": "https://registry.npmmirror.com/qs/-/qs-6.14.0.tgz", "integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/react-is": {"version": "16.13.1", "resolved": "https://registry.npmmirror.com/react-is/-/react-is-16.13.1.tgz", "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==", "license": "MIT"}, "node_modules/regenerator-runtime": {"version": "0.13.11", "resolved": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "integrity": "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==", "license": "MIT"}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-main-filename": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/require-main-filename/-/require-main-filename-2.0.0.tgz", "integrity": "sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==", "license": "ISC"}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==", "dev": true, "license": "MIT"}, "node_modules/resolve": {"version": "1.22.10", "resolved": "https://registry.npmmirror.com/resolve/-/resolve-1.22.10.tgz", "integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/rw": {"version": "1.3.3", "resolved": "https://registry.npmmirror.com/rw/-/rw-1.3.3.tgz", "integrity": "sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "dev": true, "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "license": "MIT"}, "node_modules/secure-compare": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/secure-compare/-/secure-compare-3.0.1.tgz", "integrity": "sha512-<PERSON>ckIIV90rPDcBcglUwXPF3kg0P0qmPsPXAj6BBEENQE1p5yA1xfmDJzfi1Tappj37Pv2mVbKpL3Z1T+Nn7k1Qw==", "dev": true, "license": "MIT"}, "node_modules/seedrandom": {"version": "3.0.5", "resolved": "https://registry.npmmirror.com/seedrandom/-/seedrandom-3.0.5.tgz", "integrity": "sha512-8OwmbklUNzwezjGInmZ+2clQmExQPvomqjL7LFqOYqtmuxRgQYqOD3mHaU+MvZn5FLUeVxVfQjwLZW/n/JFuqg==", "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/set-blocking": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==", "license": "ISC"}, "node_modules/setimmediate": {"version": "1.0.5", "resolved": "https://registry.npmmirror.com/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==", "license": "MIT"}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/sprintf-js": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/through": {"version": "2.3.8", "resolved": "https://registry.npmmirror.com/through/-/through-2.3.8.tgz", "integrity": "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==", "license": "MIT"}, "node_modules/topojson-client": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/topojson-client/-/topojson-client-3.1.0.tgz", "integrity": "sha512-605uxS6bcYxGXw9qi62XyrV6Q3xwbndjachmNxu8HWTtVPxZfEJN9fd/SZS1Q54Sn2y0TMyMxFj/cJINqGHrKw==", "license": "ISC", "dependencies": {"commander": "2"}, "bin": {"topo2geo": "bin/topo2geo", "topomerge": "bin/topomerge", "topoquantize": "bin/topoquantize"}}, "node_modules/tr46": {"version": "0.0.3", "resolved": "https://registry.npmmirror.com/tr46/-/tr46-0.0.3.tgz", "integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==", "license": "MIT"}, "node_modules/tslib": {"version": "2.0.3", "resolved": "https://registry.npmmirror.com/tslib/-/tslib-2.0.3.tgz", "integrity": "sha512-uZtkfKblCEQtZKBF6EBXVZeQNl82yqtDQdv+eck8u7tdPxjLu2/lp5/uPW+um2tpuxINHWy3GhiccY7QgEaVHQ==", "license": "0BSD"}, "node_modules/ua-parser-js": {"version": "0.7.40", "resolved": "https://registry.npmmirror.com/ua-parser-js/-/ua-parser-js-0.7.40.tgz", "integrity": "sha512-us1E3K+3jJppDBa3Tl0L3MOJiGhe1C6P0+nIvQAFYbxlMAx0h81eOwLmU57xgqToduDDPx3y5QsdjPfDu+FgOQ==", "funding": [{"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}, {"type": "github", "url": "https://github.com/sponsors/faisalman"}], "license": "MIT", "bin": {"ua-parser-js": "script/cli.js"}, "engines": {"node": "*"}}, "node_modules/undici-types": {"version": "7.10.0", "resolved": "https://registry.npmmirror.com/undici-types/-/undici-types-7.10.0.tgz", "integrity": "sha512-t5Fy/nfn+14LuOc2KNYg75vZqClpAiqscVvMygNnlsHBFpSXdJaYtXMcdNLpl/Qvc3P2cB3s6lOV51nqsFq4ag==", "license": "MIT"}, "node_modules/union": {"version": "0.5.0", "resolved": "https://registry.npmmirror.com/union/-/union-0.5.0.tgz", "integrity": "sha512-N6uOhuW6zO95P3Mel2I2zMsbsanvvtgn6jVqJv4vbVcz/JN0OkL9suomjQGmWtxJQXOCqUJvquc1sMeNz/IwlA==", "dev": true, "dependencies": {"qs": "^6.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/url-join": {"version": "4.0.1", "resolved": "https://registry.npmmirror.com/url-join/-/url-join-4.0.1.tgz", "integrity": "sha512-jk1+QP6ZJqyOiuEI9AEWQfju/nB2Pw466kbA0LEZljHwKeMgd9WrAEgEGxjPDD2+TNbbb37rTyhEfrCXfuKXnA==", "dev": true, "license": "MIT"}, "node_modules/vega": {"version": "5.20.0", "resolved": "https://registry.npmmirror.com/vega/-/vega-5.20.0.tgz", "integrity": "sha512-L2hDaTH2gz9DFbu7l1B8fR637HzctViuosFCo/Db5aBe93fCJ/w/oJu+vQNfQELzfm9sntkS/+A4u+39xrDCNA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"vega-crossfilter": "~4.0.5", "vega-dataflow": "~5.7.3", "vega-encode": "~4.8.3", "vega-event-selector": "~2.0.6", "vega-expression": "~4.0.1", "vega-force": "~4.0.7", "vega-format": "~1.0.4", "vega-functions": "~5.12.0", "vega-geo": "~4.3.8", "vega-hierarchy": "~4.0.9", "vega-label": "~1.0.0", "vega-loader": "~4.4.0", "vega-parser": "~6.1.3", "vega-projection": "~1.4.5", "vega-regression": "~1.0.9", "vega-runtime": "~6.1.3", "vega-scale": "~7.1.1", "vega-scenegraph": "~4.9.4", "vega-statistics": "~1.7.9", "vega-time": "~2.0.4", "vega-transforms": "~4.9.3", "vega-typings": "~0.20.0", "vega-util": "~1.16.1", "vega-view": "~5.10.0", "vega-view-transforms": "~4.5.8", "vega-voronoi": "~4.1.5", "vega-wordcloud": "~4.1.3"}}, "node_modules/vega-canvas": {"version": "1.2.7", "resolved": "https://registry.npmmirror.com/vega-canvas/-/vega-canvas-1.2.7.tgz", "integrity": "sha512-OkJ9CACVcN9R5Pi9uF6MZBF06pO6qFpDYHWSKBJsdHP5o724KrsgR6UvbnXFH82FdsiTOff/HqjuaG8C7FL+9Q==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-crossfilter": {"version": "4.0.5", "resolved": "https://registry.npmmirror.com/vega-crossfilter/-/vega-crossfilter-4.0.5.tgz", "integrity": "sha512-yF+iyGP+ZxU7Tcj5yBsMfoUHTCebTALTXIkBNA99RKdaIHp1E690UaGVLZe6xde2n5WaYpho6I/I6wdAW3NXcg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.7.1", "vega-dataflow": "^5.7.3", "vega-util": "^1.15.2"}}, "node_modules/vega-dataflow": {"version": "5.7.7", "resolved": "https://registry.npmmirror.com/vega-dataflow/-/vega-dataflow-5.7.7.tgz", "integrity": "sha512-R2NX2HvgXL+u4E6u+L5lKvvRiCtnE6N6l+umgojfi53suhhkFP+zB+2UAQo4syxuZ4763H1csfkKc4xpqLzKnw==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"vega-format": "^1.1.3", "vega-loader": "^4.5.3", "vega-util": "^1.17.3"}}, "node_modules/vega-dataflow/node_modules/commander": {"version": "7.2.0", "resolved": "https://registry.npmmirror.com/commander/-/commander-7.2.0.tgz", "integrity": "sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==", "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/vega-dataflow/node_modules/d3-array": {"version": "3.2.4", "resolved": "https://registry.npmmirror.com/d3-array/-/d3-array-3.2.4.tgz", "integrity": "sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==", "license": "ISC", "dependencies": {"internmap": "1 - 2"}, "engines": {"node": ">=12"}}, "node_modules/vega-dataflow/node_modules/d3-dsv": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/d3-dsv/-/d3-dsv-3.0.1.tgz", "integrity": "sha512-UG6OvdI5afDIFP9w4G0mNq50dSOsXHJaRE8arAS5o9ApWnIElp8GZw1Dun8vP8OyHOZ/QJUKUJwxiiCCnUwm+Q==", "license": "ISC", "dependencies": {"commander": "7", "iconv-lite": "0.6", "rw": "1"}, "bin": {"csv2json": "bin/dsv2json.js", "csv2tsv": "bin/dsv2dsv.js", "dsv2dsv": "bin/dsv2dsv.js", "dsv2json": "bin/dsv2json.js", "json2csv": "bin/json2dsv.js", "json2dsv": "bin/json2dsv.js", "json2tsv": "bin/json2dsv.js", "tsv2csv": "bin/dsv2dsv.js", "tsv2json": "bin/dsv2json.js"}, "engines": {"node": ">=12"}}, "node_modules/vega-dataflow/node_modules/d3-format": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/d3-format/-/d3-format-3.1.0.tgz", "integrity": "sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/vega-dataflow/node_modules/d3-time": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/d3-time/-/d3-time-3.1.0.tgz", "integrity": "sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==", "license": "ISC", "dependencies": {"d3-array": "2 - 3"}, "engines": {"node": ">=12"}}, "node_modules/vega-dataflow/node_modules/d3-time-format": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/d3-time-format/-/d3-time-format-4.1.0.tgz", "integrity": "sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==", "license": "ISC", "dependencies": {"d3-time": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/vega-dataflow/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/vega-dataflow/node_modules/vega-format": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/vega-format/-/vega-format-1.1.3.tgz", "integrity": "sha512-wQhw7KR46wKJAip28FF/CicW+oiJaPAwMKdrxlnTA0Nv8Bf7bloRlc+O3kON4b4H1iALLr9KgRcYTOeXNs2MOA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^3.2.2", "d3-format": "^3.1.0", "d3-time-format": "^4.1.0", "vega-time": "^2.1.3", "vega-util": "^1.17.3"}}, "node_modules/vega-dataflow/node_modules/vega-loader": {"version": "4.5.3", "resolved": "https://registry.npmmirror.com/vega-loader/-/vega-loader-4.5.3.tgz", "integrity": "sha512-dUfIpxTLF2magoMaur+jXGvwMxjtdlDZaIS8lFj6N7IhUST6nIvBzuUlRM+zLYepI5GHtCLOnqdKU4XV0NggCA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-dsv": "^3.0.1", "node-fetch": "^2.6.7", "topojson-client": "^3.1.0", "vega-format": "^1.1.3", "vega-util": "^1.17.3"}}, "node_modules/vega-dataflow/node_modules/vega-time": {"version": "2.1.3", "resolved": "https://registry.npmmirror.com/vega-time/-/vega-time-2.1.3.tgz", "integrity": "sha512-hFcWPdTV844IiY0m97+WUoMLADCp+8yUQR1NStWhzBzwDDA7QEGGwYGxALhdMOaDTwkyoNj3V/nox2rQAJD/vQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^3.2.2", "d3-time": "^3.1.0", "vega-util": "^1.17.3"}}, "node_modules/vega-dataflow/node_modules/vega-util": {"version": "1.17.3", "resolved": "https://registry.npmmirror.com/vega-util/-/vega-util-1.17.3.tgz", "integrity": "sha512-nSNpZLUrRvFo46M5OK4O6x6f08WD1yOcEzHNlqivF+sDLSsVpstaF6fdJYwrbf/debFi2L9Tkp4gZQtssup9iQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-embed": {"version": "6.17.0", "resolved": "https://registry.npmmirror.com/vega-embed/-/vega-embed-6.17.0.tgz", "integrity": "sha512-9eiVZCrLDb/EiVCMbMYouWB/q9dOeVkL5Bh0vU6wsUpIV/bbEvS47uljuo3YSxFqkfNpJ+Qt8xvLRiYSnN4lqw==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"fast-json-patch": "^3.0.0-1", "json-stringify-pretty-compact": "^3.0.0", "semver": "^7.3.5", "vega-schema-url-parser": "^2.1.0", "vega-themes": "^2.10.0", "vega-tooltip": "^0.25.1"}, "peerDependencies": {"vega": "^5.13.0", "vega-lite": "*"}}, "node_modules/vega-encode": {"version": "4.8.3", "resolved": "https://registry.npmmirror.com/vega-encode/-/vega-encode-4.8.3.tgz", "integrity": "sha512-JoRYtaV2Hs8spWLzTu/IjR7J9jqRmuIOEicAaWj6T9NSZrNWQzu2zF3IVsX85WnrIDIRUDaehXaFZvy9uv9RQg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.7.1", "d3-interpolate": "^2.0.1", "vega-dataflow": "^5.7.3", "vega-scale": "^7.0.3", "vega-util": "^1.15.2"}}, "node_modules/vega-event-selector": {"version": "2.0.6", "resolved": "https://registry.npmmirror.com/vega-event-selector/-/vega-event-selector-2.0.6.tgz", "integrity": "sha512-UwCu50Sqd8kNZ1X/XgiAY+QAyQUmGFAwyDu7y0T5fs6/TPQnDo/Bo346NgSgINBEhEKOAMY1Nd/rPOk4UEm/ew==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-expression": {"version": "4.0.1", "resolved": "https://registry.npmmirror.com/vega-expression/-/vega-expression-4.0.1.tgz", "integrity": "sha512-ZrDj0hP8NmrCpdLFf7Rd/xMUHGoSYsAOTaYp7uXZ2dkEH5x0uPy5laECMc8TiQvL8W+8IrN2HAWCMRthTSRe2Q==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"vega-util": "^1.16.0"}}, "node_modules/vega-force": {"version": "4.0.7", "resolved": "https://registry.npmmirror.com/vega-force/-/vega-force-4.0.7.tgz", "integrity": "sha512-pyLKdwXSZ9C1dVIqdJOobvBY29rLvZjvRRTla9BU/nMwAiAGlGi6WKUFdRGdneyGe3zo2nSZDTZlZM/Z5VaQNA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-force": "^2.1.1", "vega-dataflow": "^5.7.3", "vega-util": "^1.15.2"}}, "node_modules/vega-format": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/vega-format/-/vega-format-1.0.4.tgz", "integrity": "sha512-oTAeub3KWm6nKhXoYCx1q9G3K43R6/pDMXvqDlTSUtjoY7b/Gixm8iLcir5S9bPjvH40n4AcbZsPmNfL/Up77A==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.7.1", "d3-format": "^2.0.0", "d3-time-format": "^3.0.0", "vega-time": "^2.0.3", "vega-util": "^1.15.2"}}, "node_modules/vega-format/node_modules/d3-format": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/d3-format/-/d3-format-2.0.0.tgz", "integrity": "sha512-Ab3S6XuE/Q+flY96HXT0jOXcM4EAClYFnRGY5zsjRGNy6qCYrQsMffs7cV5Q9xejb35zxW5hf/guKw34kvIKsA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-functions": {"version": "5.12.1", "resolved": "https://registry.npmmirror.com/vega-functions/-/vega-functions-5.12.1.tgz", "integrity": "sha512-7cHfcjXOj27qEbh2FTzWDl7FJK4xGcMFF7+oiyqa0fp7BU/wNT5YdNV0t5kCX9WjV7mfJWACKV74usLJbyM6GA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.7.1", "d3-color": "^2.0.0", "d3-geo": "^2.0.1", "vega-dataflow": "^5.7.3", "vega-expression": "^5.0.0", "vega-scale": "^7.1.1", "vega-scenegraph": "^4.9.3", "vega-selections": "^5.3.1", "vega-statistics": "^1.7.9", "vega-time": "^2.0.4", "vega-util": "^1.16.0"}}, "node_modules/vega-functions/node_modules/vega-expression": {"version": "5.2.0", "resolved": "https://registry.npmmirror.com/vega-expression/-/vega-expression-5.2.0.tgz", "integrity": "sha512-WRMa4ny3iZIVAzDlBh3ipY2QUuLk2hnJJbfbncPgvTF7BUgbIbKq947z+JicWksYbokl8n1JHXJoqi3XvpG0Zw==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@types/estree": "^1.0.0", "vega-util": "^1.17.3"}}, "node_modules/vega-functions/node_modules/vega-util": {"version": "1.17.3", "resolved": "https://registry.npmmirror.com/vega-util/-/vega-util-1.17.3.tgz", "integrity": "sha512-nSNpZLUrRvFo46M5OK4O6x6f08WD1yOcEzHNlqivF+sDLSsVpstaF6fdJYwrbf/debFi2L9Tkp4gZQtssup9iQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-geo": {"version": "4.3.8", "resolved": "https://registry.npmmirror.com/vega-geo/-/vega-geo-4.3.8.tgz", "integrity": "sha512-fsGxV96Q/QRgPqOPtMBZdI+DneIiROKTG3YDZvGn0EdV16OG5LzFhbNgLT5GPzI+kTwgLpAsucBHklexlB4kfg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.7.1", "d3-color": "^2.0.0", "d3-geo": "^2.0.1", "vega-canvas": "^1.2.5", "vega-dataflow": "^5.7.3", "vega-projection": "^1.4.5", "vega-statistics": "^1.7.9", "vega-util": "^1.15.2"}}, "node_modules/vega-hierarchy": {"version": "4.0.9", "resolved": "https://registry.npmmirror.com/vega-hierarchy/-/vega-hierarchy-4.0.9.tgz", "integrity": "sha512-4XaWK6V38/QOZ+vllKKTafiwL25m8Kd+ebHmDV+Q236ONHmqc/gv82wwn9nBeXPEfPv4FyJw2SRoqa2Jol6fug==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-hierarchy": "^2.0.0", "vega-dataflow": "^5.7.3", "vega-util": "^1.15.2"}}, "node_modules/vega-label": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/vega-label/-/vega-label-1.0.0.tgz", "integrity": "sha512-hCdm2pcHgkKgxnzW9GvX5JmYNiUMlOXOibtMmBzvFBQHX3NiV9giQ5nsPiQiFbV08VxEPtM+VYXr2HyrIcq5zQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"vega-canvas": "^1.2.5", "vega-dataflow": "^5.7.3", "vega-scenegraph": "^4.9.2", "vega-util": "^1.15.2"}}, "node_modules/vega-lite": {"version": "4.13.1", "resolved": "https://registry.npmmirror.com/vega-lite/-/vega-lite-4.13.1.tgz", "integrity": "sha512-OHZSSqVLuikoZ3idz3jIRk0UCKtVU2Lq5gaD6cLNTnJjNetoHKKdfZ023LVj4+Y9yWPz5meb+EJUsfBAGfF4Vw==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@types/clone": "~0.1.30", "@types/fast-json-stable-stringify": "^2.0.0", "array-flat-polyfill": "^1.0.1", "clone": "~2.1.2", "fast-deep-equal": "~3.1.1", "fast-json-stable-stringify": "~2.1.0", "json-stringify-pretty-compact": "~2.0.0", "tslib": "~2.0.0", "vega-event-selector": "~2.0.3", "vega-expression": "~2.6.5", "vega-util": "~1.14.0", "yargs": "~15.3.1"}, "bin": {"vl2pdf": "bin/vl2pdf", "vl2png": "bin/vl2png", "vl2svg": "bin/vl2svg", "vl2vg": "bin/vl2vg"}, "peerDependencies": {"vega": "^5.12.1"}}, "node_modules/vega-lite/node_modules/cliui": {"version": "6.0.0", "resolved": "https://registry.npmmirror.com/cliui/-/cliui-6.0.0.tgz", "integrity": "sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^6.2.0"}}, "node_modules/vega-lite/node_modules/json-stringify-pretty-compact": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/json-stringify-pretty-compact/-/json-stringify-pretty-compact-2.0.0.tgz", "integrity": "sha512-WRitRfs6BGq4q8gTgOy4ek7iPFXjbra0H3PmDLKm2xnZ+Gh1HUhiKGgCZkSPNULlP7mvfu6FV/mOLhCarspADQ==", "license": "MIT"}, "node_modules/vega-lite/node_modules/vega-expression": {"version": "2.6.6", "resolved": "https://registry.npmmirror.com/vega-expression/-/vega-expression-2.6.6.tgz", "integrity": "sha512-zxPzXO33FawU3WQHRmHJaRreyJlyMaNMn1uuCFSouJttPkBBWB5gCrha2f5+pF3t4NMFWTnSrgCkR6mcaubnng==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"vega-util": "^1.15.0"}}, "node_modules/vega-lite/node_modules/vega-expression/node_modules/vega-util": {"version": "1.17.3", "resolved": "https://registry.npmmirror.com/vega-util/-/vega-util-1.17.3.tgz", "integrity": "sha512-nSNpZLUrRvFo46M5OK4O6x6f08WD1yOcEzHNlqivF+sDLSsVpstaF6fdJYwrbf/debFi2L9Tkp4gZQtssup9iQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-lite/node_modules/vega-util": {"version": "1.14.1", "resolved": "https://registry.npmmirror.com/vega-util/-/vega-util-1.14.1.tgz", "integrity": "sha512-pSKJ8OCkgfgHZDTljyj+gmGltgulceWbk1BV6LWrXqp6P3J8qPA/oZA8+a93YNApYxXZ3yzIVUDOo5O27xk0jw==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-lite/node_modules/wrap-ansi": {"version": "6.2.0", "resolved": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "integrity": "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/vega-lite/node_modules/y18n": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/y18n/-/y18n-4.0.3.tgz", "integrity": "sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==", "license": "ISC"}, "node_modules/vega-lite/node_modules/yargs": {"version": "15.3.1", "resolved": "https://registry.npmmirror.com/yargs/-/yargs-15.3.1.tgz", "integrity": "sha512-92O1HWEjw27sBfgmXiixJWT5hRBp2eobqXicLtPBIDBhYB+1HpwZlXmbW2luivBJHBzki+7VyCLRtAkScbTBQA==", "license": "MIT", "dependencies": {"cliui": "^6.0.0", "decamelize": "^1.2.0", "find-up": "^4.1.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^4.2.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^18.1.1"}, "engines": {"node": ">=8"}}, "node_modules/vega-lite/node_modules/yargs-parser": {"version": "18.1.3", "resolved": "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-18.1.3.tgz", "integrity": "sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==", "license": "ISC", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "engines": {"node": ">=6"}}, "node_modules/vega-loader": {"version": "4.4.1", "resolved": "https://registry.npmmirror.com/vega-loader/-/vega-loader-4.4.1.tgz", "integrity": "sha512-dj65i4qlNhK0mOmjuchHgUrF5YUaWrYpx0A8kXA68lBk5Hkx8FNRztkcl07CZJ1+8V81ymEyJii9jzGbhEX0ag==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-dsv": "^2.0.0", "node-fetch": "^2.6.1", "topojson-client": "^3.1.0", "vega-format": "^1.0.4", "vega-util": "^1.16.0"}}, "node_modules/vega-parser": {"version": "6.1.4", "resolved": "https://registry.npmmirror.com/vega-parser/-/vega-parser-6.1.4.tgz", "integrity": "sha512-tORdpWXiH/kkXcpNdbSVEvtaxBuuDtgYp9rBunVW9oLsjFvFXbSWlM1wvJ9ZFSaTfx6CqyTyGMiJemmr1QnTjQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"vega-dataflow": "^5.7.3", "vega-event-selector": "^3.0.0", "vega-functions": "^5.12.1", "vega-scale": "^7.1.1", "vega-util": "^1.16.0"}}, "node_modules/vega-parser/node_modules/vega-event-selector": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/vega-event-selector/-/vega-event-selector-3.0.1.tgz", "integrity": "sha512-K5zd7s5tjr1LiOOkjGpcVls8GsH/f2CWCrWcpKy74gTCp+llCdwz0Enqo013ZlGaRNjfgD/o1caJRt3GSaec4A==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-projection": {"version": "1.4.5", "resolved": "https://registry.npmmirror.com/vega-projection/-/vega-projection-1.4.5.tgz", "integrity": "sha512-85kWcPv0zrrNfxescqHtSYpRknilrS0K3CVRZc7IYQxnLtL1oma9WEbrSr1LCmDoCP5hl2Z1kKbomPXkrQX5Ag==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-geo": "^2.0.1", "d3-geo-projection": "^3.0.0"}}, "node_modules/vega-regression": {"version": "1.0.9", "resolved": "https://registry.npmmirror.com/vega-regression/-/vega-regression-1.0.9.tgz", "integrity": "sha512-KSr3QbCF0vJEAWFVY2MA9X786oiJncTTr3gqRMPoaLr/Yo3f7OPKXRoUcw36RiWa0WCOEMgTYtM28iK6ZuSgaA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.7.1", "vega-dataflow": "^5.7.3", "vega-statistics": "^1.7.9", "vega-util": "^1.15.2"}}, "node_modules/vega-runtime": {"version": "6.1.4", "resolved": "https://registry.npmmirror.com/vega-runtime/-/vega-runtime-6.1.4.tgz", "integrity": "sha512-0dDYXyFLQcxPQ2OQU0WuBVYLRZnm+/CwVu6i6N4idS7R9VXIX5581EkCh3pZ20pQ/+oaA7oJ0pR9rJgJ6rukRQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"vega-dataflow": "^5.7.5", "vega-util": "^1.17.1"}}, "node_modules/vega-runtime/node_modules/vega-util": {"version": "1.17.3", "resolved": "https://registry.npmmirror.com/vega-util/-/vega-util-1.17.3.tgz", "integrity": "sha512-nSNpZLUrRvFo46M5OK4O6x6f08WD1yOcEzHNlqivF+sDLSsVpstaF6fdJYwrbf/debFi2L9Tkp4gZQtssup9iQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-scale": {"version": "7.1.1", "resolved": "https://registry.npmmirror.com/vega-scale/-/vega-scale-7.1.1.tgz", "integrity": "sha512-yE0to0prA9E5PBJ/XP77TO0BMkzyUVyt7TH5PAwj+CZT7PMsMO6ozihelRhoIiVcP0Ae/ByCEQBUQkzN5zJ0ZA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.7.1", "d3-interpolate": "^2.0.1", "d3-scale": "^3.2.2", "vega-time": "^2.0.4", "vega-util": "^1.15.2"}}, "node_modules/vega-scenegraph": {"version": "4.9.4", "resolved": "https://registry.npmmirror.com/vega-scenegraph/-/vega-scenegraph-4.9.4.tgz", "integrity": "sha512-QaegQzbFE2yhYLNWAmHwAuguW3yTtQrmwvfxYT8tk0g+KKodrQ5WSmNrphWXhqwtsgVSvtdZkfp2IPeumcOQJg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-path": "^2.0.0", "d3-shape": "^2.0.0", "vega-canvas": "^1.2.5", "vega-loader": "^4.3.3", "vega-scale": "^7.1.1", "vega-util": "^1.15.2"}}, "node_modules/vega-schema-url-parser": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/vega-schema-url-parser/-/vega-schema-url-parser-2.2.0.tgz", "integrity": "sha512-yAtdBnfYOhECv9YC70H2gEiqfIbVkq09aaE4y/9V/ovEFmH9gPKaEgzIZqgT7PSPQjKhsNkb6jk6XvSoboxOBw==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-selections": {"version": "5.6.0", "resolved": "https://registry.npmmirror.com/vega-selections/-/vega-selections-5.6.0.tgz", "integrity": "sha512-UE2w78rUUbaV3Ph+vQbQDwh8eywIJYRxBiZdxEG/Tr/KtFMLdy2BDgNZuuDO1Nv8jImPJwONmqjNhNDYwM0VJQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "3.2.4", "vega-expression": "^5.2.0", "vega-util": "^1.17.3"}}, "node_modules/vega-selections/node_modules/d3-array": {"version": "3.2.4", "resolved": "https://registry.npmmirror.com/d3-array/-/d3-array-3.2.4.tgz", "integrity": "sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==", "license": "ISC", "dependencies": {"internmap": "1 - 2"}, "engines": {"node": ">=12"}}, "node_modules/vega-selections/node_modules/vega-expression": {"version": "5.2.0", "resolved": "https://registry.npmmirror.com/vega-expression/-/vega-expression-5.2.0.tgz", "integrity": "sha512-WRMa4ny3iZIVAzDlBh3ipY2QUuLk2hnJJbfbncPgvTF7BUgbIbKq947z+JicWksYbokl8n1JHXJoqi3XvpG0Zw==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@types/estree": "^1.0.0", "vega-util": "^1.17.3"}}, "node_modules/vega-selections/node_modules/vega-util": {"version": "1.17.3", "resolved": "https://registry.npmmirror.com/vega-util/-/vega-util-1.17.3.tgz", "integrity": "sha512-nSNpZLUrRvFo46M5OK4O6x6f08WD1yOcEzHNlqivF+sDLSsVpstaF6fdJYwrbf/debFi2L9Tkp4gZQtssup9iQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-statistics": {"version": "1.7.10", "resolved": "https://registry.npmmirror.com/vega-statistics/-/vega-statistics-1.7.10.tgz", "integrity": "sha512-QLb12gcfpDZ9K5h3TLGrlz4UXDH9wSPyg9LLfOJZacxvvJEPohacUQNrGEAVtFO9ccUCerRfH9cs25ZtHsOZrw==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.7.1"}}, "node_modules/vega-themes": {"version": "2.15.0", "resolved": "https://registry.npmmirror.com/vega-themes/-/vega-themes-2.15.0.tgz", "integrity": "sha512-DicRAKG9z+23A+rH/3w3QjJvKnlGhSbbUXGjBvYGseZ1lvj9KQ0BXZ2NS/+MKns59LNpFNHGi9us/wMlci4TOA==", "license": "BSD-3-<PERSON><PERSON>", "peerDependencies": {"vega": "*", "vega-lite": "*"}}, "node_modules/vega-time": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/vega-time/-/vega-time-2.0.4.tgz", "integrity": "sha512-U314UDR9+ZlWrD3KBaeH+j/c2WSMdvcZq5yJfFT0yTg1jsBKAQBYFGvl+orackD8Zx3FveHOxx3XAObaQeDX+Q==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.7.1", "d3-time": "^2.0.0", "vega-util": "^1.15.2"}}, "node_modules/vega-tooltip": {"version": "0.25.1", "resolved": "https://registry.npmmirror.com/vega-tooltip/-/vega-tooltip-0.25.1.tgz", "integrity": "sha512-ugGwGi2/p3OpB8N15xieuzP8DyV5DreqMWcmJ9zpWT8GlkyKtef4dGRXnvHeHQ+iJFmWrq4oZJ+kLTrdiECjAg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"vega-util": "^1.16.0"}}, "node_modules/vega-transforms": {"version": "4.9.4", "resolved": "https://registry.npmmirror.com/vega-transforms/-/vega-transforms-4.9.4.tgz", "integrity": "sha512-JGBhm5Bf6fiGTUSB5Qr5ckw/KU9FJcSV5xIe/y4IobM/i/KNwI1i1fP45LzP4F4yZc0DMTwJod2UvFHGk9plKA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.7.1", "vega-dataflow": "^5.7.4", "vega-statistics": "^1.7.9", "vega-time": "^2.0.4", "vega-util": "^1.16.1"}}, "node_modules/vega-typings": {"version": "0.20.0", "resolved": "https://registry.npmmirror.com/vega-typings/-/vega-typings-0.20.0.tgz", "integrity": "sha512-S+HIRN/3WYiS5zrQjJ4FDEOlvFVHLxPXMJerrnN3YZ6bxCDYo7tEvQUUuByGZ3d19GuKjgejczWS7XHvF3WjDw==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"vega-util": "^1.15.2"}}, "node_modules/vega-util": {"version": "1.16.1", "resolved": "https://registry.npmmirror.com/vega-util/-/vega-util-1.16.1.tgz", "integrity": "sha512-FdgD72fmZMPJE99FxvFXth0IL4BbLA93WmBg/lvcJmfkK4Uf90WIlvGwaIUdSePIsdpkZjBPyQcHMQ8OcS8Smg==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-view": {"version": "5.10.1", "resolved": "https://registry.npmmirror.com/vega-view/-/vega-view-5.10.1.tgz", "integrity": "sha512-4xvQ5KZcgKdZx1Z7jjenCUumvlyr/j4XcHLRf9gyeFrFvvS596dVpL92V8twhV6O++DmS2+fj+rHagO8Di4nMg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^2.7.1", "d3-timer": "^2.0.0", "vega-dataflow": "^5.7.3", "vega-format": "^1.0.4", "vega-functions": "^5.10.0", "vega-runtime": "^6.1.3", "vega-scenegraph": "^4.9.4", "vega-util": "^1.16.1"}}, "node_modules/vega-view-transforms": {"version": "4.5.9", "resolved": "https://registry.npmmirror.com/vega-view-transforms/-/vega-view-transforms-4.5.9.tgz", "integrity": "sha512-NxEq4ZD4QwWGRrl2yDLnBRXM9FgCI+vvYb3ZC2+nVDtkUxOlEIKZsMMw31op5GZpfClWLbjCT3mVvzO2xaTF+g==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"vega-dataflow": "^5.7.5", "vega-scenegraph": "^4.10.2", "vega-util": "^1.17.1"}}, "node_modules/vega-view-transforms/node_modules/commander": {"version": "7.2.0", "resolved": "https://registry.npmmirror.com/commander/-/commander-7.2.0.tgz", "integrity": "sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==", "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/vega-view-transforms/node_modules/d3-array": {"version": "3.2.4", "resolved": "https://registry.npmmirror.com/d3-array/-/d3-array-3.2.4.tgz", "integrity": "sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==", "license": "ISC", "dependencies": {"internmap": "1 - 2"}, "engines": {"node": ">=12"}}, "node_modules/vega-view-transforms/node_modules/d3-dsv": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/d3-dsv/-/d3-dsv-3.0.1.tgz", "integrity": "sha512-UG6OvdI5afDIFP9w4G0mNq50dSOsXHJaRE8arAS5o9ApWnIElp8GZw1Dun8vP8OyHOZ/QJUKUJwxiiCCnUwm+Q==", "license": "ISC", "dependencies": {"commander": "7", "iconv-lite": "0.6", "rw": "1"}, "bin": {"csv2json": "bin/dsv2json.js", "csv2tsv": "bin/dsv2dsv.js", "dsv2dsv": "bin/dsv2dsv.js", "dsv2json": "bin/dsv2json.js", "json2csv": "bin/json2dsv.js", "json2dsv": "bin/json2dsv.js", "json2tsv": "bin/json2dsv.js", "tsv2csv": "bin/dsv2dsv.js", "tsv2json": "bin/dsv2json.js"}, "engines": {"node": ">=12"}}, "node_modules/vega-view-transforms/node_modules/d3-format": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/d3-format/-/d3-format-3.1.0.tgz", "integrity": "sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/vega-view-transforms/node_modules/d3-interpolate": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/d3-interpolate/-/d3-interpolate-3.0.1.tgz", "integrity": "sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==", "license": "ISC", "dependencies": {"d3-color": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/vega-view-transforms/node_modules/d3-path": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/d3-path/-/d3-path-3.1.0.tgz", "integrity": "sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/vega-view-transforms/node_modules/d3-scale": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/d3-scale/-/d3-scale-4.0.2.tgz", "integrity": "sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==", "license": "ISC", "dependencies": {"d3-array": "2.10.0 - 3", "d3-format": "1 - 3", "d3-interpolate": "1.2.0 - 3", "d3-time": "2.1.1 - 3", "d3-time-format": "2 - 4"}, "engines": {"node": ">=12"}}, "node_modules/vega-view-transforms/node_modules/d3-shape": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/d3-shape/-/d3-shape-3.2.0.tgz", "integrity": "sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==", "license": "ISC", "dependencies": {"d3-path": "^3.1.0"}, "engines": {"node": ">=12"}}, "node_modules/vega-view-transforms/node_modules/d3-time": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/d3-time/-/d3-time-3.1.0.tgz", "integrity": "sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==", "license": "ISC", "dependencies": {"d3-array": "2 - 3"}, "engines": {"node": ">=12"}}, "node_modules/vega-view-transforms/node_modules/d3-time-format": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/d3-time-format/-/d3-time-format-4.1.0.tgz", "integrity": "sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==", "license": "ISC", "dependencies": {"d3-time": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/vega-view-transforms/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/vega-view-transforms/node_modules/vega-format": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/vega-format/-/vega-format-1.1.3.tgz", "integrity": "sha512-wQhw7KR46wKJAip28FF/CicW+oiJaPAwMKdrxlnTA0Nv8Bf7bloRlc+O3kON4b4H1iALLr9KgRcYTOeXNs2MOA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^3.2.2", "d3-format": "^3.1.0", "d3-time-format": "^4.1.0", "vega-time": "^2.1.3", "vega-util": "^1.17.3"}}, "node_modules/vega-view-transforms/node_modules/vega-loader": {"version": "4.5.3", "resolved": "https://registry.npmmirror.com/vega-loader/-/vega-loader-4.5.3.tgz", "integrity": "sha512-dUfIpxTLF2magoMaur+jXGvwMxjtdlDZaIS8lFj6N7IhUST6nIvBzuUlRM+zLYepI5GHtCLOnqdKU4XV0NggCA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-dsv": "^3.0.1", "node-fetch": "^2.6.7", "topojson-client": "^3.1.0", "vega-format": "^1.1.3", "vega-util": "^1.17.3"}}, "node_modules/vega-view-transforms/node_modules/vega-scale": {"version": "7.4.2", "resolved": "https://registry.npmmirror.com/vega-scale/-/vega-scale-7.4.2.tgz", "integrity": "sha512-o6Hl76aU1jlCK7Q8DPYZ8OGsp4PtzLdzI6nGpLt8rxoE78QuB3GBGEwGAQJitp4IF7Lb2rL5oAXEl3ZP6xf9jg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^3.2.2", "d3-interpolate": "^3.0.1", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.1.0", "vega-time": "^2.1.3", "vega-util": "^1.17.3"}}, "node_modules/vega-view-transforms/node_modules/vega-scenegraph": {"version": "4.13.1", "resolved": "https://registry.npmmirror.com/vega-scenegraph/-/vega-scenegraph-4.13.1.tgz", "integrity": "sha512-LFY9+sLIxRfdDI9ZTKjLoijMkIAzPLBWHpPkwv4NPYgdyx+0qFmv+puBpAUGUY9VZqAZ736Uj5NJY9zw+/M3yQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-path": "^3.1.0", "d3-shape": "^3.2.0", "vega-canvas": "^1.2.7", "vega-loader": "^4.5.3", "vega-scale": "^7.4.2", "vega-util": "^1.17.3"}}, "node_modules/vega-view-transforms/node_modules/vega-time": {"version": "2.1.3", "resolved": "https://registry.npmmirror.com/vega-time/-/vega-time-2.1.3.tgz", "integrity": "sha512-hFcWPdTV844IiY0m97+WUoMLADCp+8yUQR1NStWhzBzwDDA7QEGGwYGxALhdMOaDTwkyoNj3V/nox2rQAJD/vQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^3.2.2", "d3-time": "^3.1.0", "vega-util": "^1.17.3"}}, "node_modules/vega-view-transforms/node_modules/vega-util": {"version": "1.17.3", "resolved": "https://registry.npmmirror.com/vega-util/-/vega-util-1.17.3.tgz", "integrity": "sha512-nSNpZLUrRvFo46M5OK4O6x6f08WD1yOcEzHNlqivF+sDLSsVpstaF6fdJYwrbf/debFi2L9Tkp4gZQtssup9iQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/vega-voronoi": {"version": "4.1.5", "resolved": "https://registry.npmmirror.com/vega-voronoi/-/vega-voronoi-4.1.5.tgz", "integrity": "sha512-950IkgCFLj0zG33EWLAm1hZcp+FMqWcNQliMYt+MJzOD5S4MSpZpZ7K4wp2M1Jktjw/CLKFL9n38JCI0i3UonA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-delaunay": "^5.3.0", "vega-dataflow": "^5.7.3", "vega-util": "^1.15.2"}}, "node_modules/vega-wordcloud": {"version": "4.1.6", "resolved": "https://registry.npmmirror.com/vega-wordcloud/-/vega-wordcloud-4.1.6.tgz", "integrity": "sha512-lFmF3u9/ozU0P+WqPjeThQfZm0PigdbXDwpIUCxczrCXKYJLYFmZuZLZR7cxtmpZ0/yuvRvAJ4g123LXbSZF8A==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"vega-canvas": "^1.2.7", "vega-dataflow": "^5.7.7", "vega-scale": "^7.4.2", "vega-statistics": "^1.9.0", "vega-util": "^1.17.3"}}, "node_modules/vega-wordcloud/node_modules/d3-array": {"version": "3.2.4", "resolved": "https://registry.npmmirror.com/d3-array/-/d3-array-3.2.4.tgz", "integrity": "sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==", "license": "ISC", "dependencies": {"internmap": "1 - 2"}, "engines": {"node": ">=12"}}, "node_modules/vega-wordcloud/node_modules/d3-interpolate": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/d3-interpolate/-/d3-interpolate-3.0.1.tgz", "integrity": "sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==", "license": "ISC", "dependencies": {"d3-color": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/vega-wordcloud/node_modules/d3-scale": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/d3-scale/-/d3-scale-4.0.2.tgz", "integrity": "sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==", "license": "ISC", "dependencies": {"d3-array": "2.10.0 - 3", "d3-format": "1 - 3", "d3-interpolate": "1.2.0 - 3", "d3-time": "2.1.1 - 3", "d3-time-format": "2 - 4"}, "engines": {"node": ">=12"}}, "node_modules/vega-wordcloud/node_modules/d3-time": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/d3-time/-/d3-time-3.1.0.tgz", "integrity": "sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==", "license": "ISC", "dependencies": {"d3-array": "2 - 3"}, "engines": {"node": ">=12"}}, "node_modules/vega-wordcloud/node_modules/vega-scale": {"version": "7.4.2", "resolved": "https://registry.npmmirror.com/vega-scale/-/vega-scale-7.4.2.tgz", "integrity": "sha512-o6Hl76aU1jlCK7Q8DPYZ8OGsp4PtzLdzI6nGpLt8rxoE78QuB3GBGEwGAQJitp4IF7Lb2rL5oAXEl3ZP6xf9jg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^3.2.2", "d3-interpolate": "^3.0.1", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.1.0", "vega-time": "^2.1.3", "vega-util": "^1.17.3"}}, "node_modules/vega-wordcloud/node_modules/vega-statistics": {"version": "1.9.0", "resolved": "https://registry.npmmirror.com/vega-statistics/-/vega-statistics-1.9.0.tgz", "integrity": "sha512-GAqS7mkatpXcMCQKWtFu1eMUKLUymjInU0O8kXshWaQrVWjPIO2lllZ1VNhdgE0qGj4oOIRRS11kzuijLshGXQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^3.2.2"}}, "node_modules/vega-wordcloud/node_modules/vega-time": {"version": "2.1.3", "resolved": "https://registry.npmmirror.com/vega-time/-/vega-time-2.1.3.tgz", "integrity": "sha512-hFcWPdTV844IiY0m97+WUoMLADCp+8yUQR1NStWhzBzwDDA7QEGGwYGxALhdMOaDTwkyoNj3V/nox2rQAJD/vQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"d3-array": "^3.2.2", "d3-time": "^3.1.0", "vega-util": "^1.17.3"}}, "node_modules/vega-wordcloud/node_modules/vega-util": {"version": "1.17.3", "resolved": "https://registry.npmmirror.com/vega-util/-/vega-util-1.17.3.tgz", "integrity": "sha512-nSNpZLUrRvFo46M5OK4O6x6f08WD1yOcEzHNlqivF+sDLSsVpstaF6fdJYwrbf/debFi2L9Tkp4gZQtssup9iQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/whatwg-encoding": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz", "integrity": "sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==", "dev": true, "license": "MIT", "dependencies": {"iconv-lite": "0.6.3"}, "engines": {"node": ">=12"}}, "node_modules/whatwg-encoding/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/whatwg-fetch": {"version": "3.6.20", "resolved": "https://registry.npmmirror.com/whatwg-fetch/-/whatwg-fetch-3.6.20.tgz", "integrity": "sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==", "license": "MIT"}, "node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/which-module": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/which-module/-/which-module-2.0.1.tgz", "integrity": "sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==", "license": "ISC"}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://registry.npmmirror.com/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yargs": {"version": "16.2.0", "resolved": "https://registry.npmmirror.com/yargs/-/yargs-16.2.0.tgz", "integrity": "sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==", "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/yargs-parser": {"version": "20.2.9", "resolved": "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-20.2.9.tgz", "integrity": "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==", "license": "ISC", "engines": {"node": ">=10"}}}}