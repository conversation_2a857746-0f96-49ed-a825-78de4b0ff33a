/**
 * 反转链表动画控制器
 * 负责管理可视化演示的所有交互和动画效果
 */
class LinkedListAnimator {
  constructor() {
    this.originalList = null; // 原始链表
    this.currentList = null; // 当前处理的链表
    this.animationSteps = []; // 动画步骤数组
    this.currentStep = 0; // 当前步骤索引
    this.isAnimating = false; // 是否正在动画中
    this.isPaused = false; // 是否暂停
    this.animationSpeed = 1000; // 动画速度（毫秒）
    this.animationTimer = null; // 动画定时器

    this.initializeElements();
    this.bindEvents();
  }

  /**
   * 初始化DOM元素引用
   */
  initializeElements() {
    // 输入控制元素
    this.listInput = document.getElementById("listInput");
    this.createListBtn = document.getElementById("createListBtn");
    this.algorithmRadios = document.querySelectorAll('input[name="algorithm"]');

    // 控制按钮
    this.startBtn = document.getElementById("startBtn");
    this.pauseBtn = document.getElementById("pauseBtn");
    this.resetBtn = document.getElementById("resetBtn");
    this.stepBtn = document.getElementById("stepBtn");

    // 速度控制
    this.speedSlider = document.getElementById("speedSlider");
    this.speedValue = document.getElementById("speedValue");

    // 可视化容器
    this.originalListContainer = document.getElementById("originalList");
    this.processListContainer = document.getElementById("processList");
    this.resultListContainer = document.getElementById("resultList");

    // 指针元素
    this.prevPointer = document.getElementById("prevPointer");
    this.currentPointer = document.getElementById("currentPointer");
    this.nextPointer = document.getElementById("nextPointer");

    // 步骤说明
    this.stepDescription = document.getElementById("stepDescription");
    this.stepCounter = document.getElementById("stepCounter");
    this.totalSteps = document.getElementById("totalSteps");

    // 代码标签页
    this.tabBtns = document.querySelectorAll(".tab-btn");
    this.codeContents = document.querySelectorAll(".code-content");
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 创建链表按钮
    this.createListBtn.addEventListener("click", () => this.createList());

    // 输入框回车键
    this.listInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") this.createList();
    });

    // 控制按钮
    this.startBtn.addEventListener("click", () => this.startAnimation());
    this.pauseBtn.addEventListener("click", () => this.pauseAnimation());
    this.resetBtn.addEventListener("click", () => this.resetAnimation());
    this.stepBtn.addEventListener("click", () => this.stepForward());

    // 速度滑块
    this.speedSlider.addEventListener("input", (e) => {
      const speed = parseInt(e.target.value);
      this.speedValue.textContent = speed;
      this.animationSpeed = 2000 - speed * 180; // 速度越高，间隔越短
    });

    // 代码标签页切换
    this.tabBtns.forEach((btn) => {
      btn.addEventListener("click", (e) => {
        const tabId = e.target.dataset.tab;
        this.switchTab(tabId);
      });
    });

    // 初始化时创建默认链表
    this.createList();
  }

  /**
   * 从输入创建链表
   */
  createList() {
    const input = this.listInput.value.trim();
    if (!input) {
      this.showMessage("请输入链表数据", "error");
      return;
    }

    try {
      // 解析输入数据
      const values = input.split(",").map((val) => {
        const num = val.trim();
        if (num === "") throw new Error("空值");
        return isNaN(num) ? num : parseInt(num);
      });

      if (values.length === 0) {
        throw new Error("链表不能为空");
      }

      // 创建链表
      this.originalList = LinkedList.fromArray(values);
      this.currentList = LinkedList.fromArray(values); // 创建副本用于处理

      // 重置动画状态
      this.resetAnimation();

      // 渲染原始链表
      this.renderList(
        this.originalListContainer,
        this.originalList,
        "original"
      );

      this.showMessage(`成功创建包含 ${values.length} 个节点的链表`, "success");
    } catch (error) {
      this.showMessage(`输入格式错误: ${error.message}`, "error");
    }
  }

  /**
   * 渲染链表到指定容器
   * @param {HTMLElement} container - 目标容器
   * @param {ListNode} head - 链表头节点
   * @param {string} type - 链表类型 ('original', 'process', 'result')
   */
  renderList(container, head, type = "process") {
    container.innerHTML = "";

    if (!head) {
      container.innerHTML = '<div class="empty-list">空链表</div>';
      return;
    }

    const listElement = document.createElement("div");
    listElement.className = "linked-list";

    let current = head;
    let nodeIndex = 0;

    while (current) {
      // 创建节点容器
      const nodeElement = document.createElement("div");
      nodeElement.className = "node";
      nodeElement.dataset.index = nodeIndex;

      // 创建节点框
      const nodeBox = document.createElement("div");
      nodeBox.className = "node-box";
      nodeBox.textContent = current.val;
      nodeElement.appendChild(nodeBox);

      // 如果不是最后一个节点，添加箭头
      if (current.next) {
        const arrow = document.createElement("div");
        arrow.className = "arrow";
        nodeElement.appendChild(arrow);
      }

      listElement.appendChild(nodeElement);
      current = current.next;
      nodeIndex++;
    }

    // 添加null指示
    const nullElement = document.createElement("div");
    nullElement.className = "null-indicator";
    nullElement.textContent = "null";
    nullElement.style.color = "#6c757d";
    nullElement.style.fontWeight = "bold";
    nullElement.style.padding = "10px";
    listElement.appendChild(nullElement);

    container.appendChild(listElement);
  }

  /**
   * 开始动画演示
   */
  startAnimation() {
    if (!this.originalList) {
      this.showMessage("请先创建链表", "error");
      return;
    }

    if (this.isAnimating && !this.isPaused) {
      return; // 已经在运行
    }

    if (this.isPaused) {
      // 继续暂停的动画
      this.isPaused = false;
      this.updateControlButtons();
      this.continueAnimation();
      return;
    }

    // 获取选择的算法
    const selectedAlgorithm = document.querySelector(
      'input[name="algorithm"]:checked'
    ).value;

    // 重置状态
    this.resetAnimation();

    // 生成动画步骤
    this.generateAnimationSteps(selectedAlgorithm);

    // 开始执行动画
    this.isAnimating = true;
    this.updateControlButtons();
    this.executeAnimationStep();
  }

  /**
   * 生成动画步骤
   * @param {string} algorithm - 算法类型 ('iterative' 或 'recursive')
   */
  generateAnimationSteps(algorithm) {
    this.animationSteps = [];

    if (algorithm === "iterative") {
      this.generateIterativeSteps();
    } else {
      this.generateRecursiveSteps();
    }

    this.totalSteps.textContent = this.animationSteps.length;
  }

  /**
   * 生成迭代算法的动画步骤
   */
  generateIterativeSteps() {
    const values = LinkedList.toArray(this.originalList);
    let prev = null;
    let currentIndex = 0;

    // 初始状态
    this.animationSteps.push({
      type: "init",
      description: "初始化：prev = null, current = head",
      prev: null,
      current: currentIndex,
      next: currentIndex < values.length - 1 ? currentIndex + 1 : null,
      listState: [...values],
    });

    // 迭代过程
    while (currentIndex < values.length) {
      const nextIndex =
        currentIndex < values.length - 1 ? currentIndex + 1 : null;

      // 保存next指针
      this.animationSteps.push({
        type: "save_next",
        description: `保存next指针：next = current.next (${
          nextIndex !== null ? values[nextIndex] : "null"
        })`,
        prev: prev,
        current: currentIndex,
        next: nextIndex,
        listState: [...values],
      });

      // 反转指针
      this.animationSteps.push({
        type: "reverse_pointer",
        description: `反转指针：current.next = prev`,
        prev: prev,
        current: currentIndex,
        next: nextIndex,
        listState: [...values],
        reversed: true,
      });

      // 移动指针
      prev = currentIndex;
      currentIndex = nextIndex;

      if (currentIndex !== null) {
        this.animationSteps.push({
          type: "move_pointers",
          description: `移动指针：prev = current, current = next`,
          prev: prev,
          current: currentIndex,
          next: currentIndex < values.length - 1 ? currentIndex + 1 : null,
          listState: [...values],
        });
      }
    }

    // 完成
    this.animationSteps.push({
      type: "complete",
      description: "反转完成！prev现在指向新的头节点",
      prev: prev,
      current: null,
      next: null,
      listState: [...values].reverse(),
    });
  }

  /**
   * 生成递归算法的动画步骤
   */
  generateRecursiveSteps() {
    const values = LinkedList.toArray(this.originalList);

    // 递归算法的步骤生成比较复杂，这里简化处理
    this.animationSteps.push({
      type: "init",
      description: "递归方法：从头节点开始递归",
      current: 0,
      listState: [...values],
    });

    // 递归到末尾
    for (let i = 0; i < values.length - 1; i++) {
      this.animationSteps.push({
        type: "recurse",
        description: `递归调用：处理节点 ${values[i]}`,
        current: i,
        listState: [...values],
      });
    }

    // 从末尾开始反转
    for (let i = values.length - 1; i > 0; i--) {
      this.animationSteps.push({
        type: "reverse_recursive",
        description: `反转连接：${values[i]} -> ${values[i - 1]} 变为 ${
          values[i - 1]
        } -> ${values[i]}`,
        current: i - 1,
        listState: [...values],
      });
    }

    this.animationSteps.push({
      type: "complete",
      description: "递归反转完成！",
      listState: [...values].reverse(),
    });
  }

  /**
   * 执行动画步骤
   */
  executeAnimationStep() {
    if (this.currentStep >= this.animationSteps.length) {
      this.completeAnimation();
      return;
    }

    const step = this.animationSteps[this.currentStep];
    this.renderAnimationStep(step);

    this.currentStep++;
    this.stepCounter.textContent = this.currentStep;

    if (this.isAnimating && !this.isPaused) {
      this.animationTimer = setTimeout(() => {
        this.executeAnimationStep();
      }, this.animationSpeed);
    }
  }

  /**
   * 渲染动画步骤
   * @param {Object} step - 动画步骤对象
   */
  renderAnimationStep(step) {
    // 更新步骤描述
    this.stepDescription.textContent = step.description;

    // 渲染处理过程中的链表
    this.renderProcessList(step);

    // 更新指针位置
    this.updatePointers(step);

    // 如果是完成步骤，渲染结果
    if (step.type === "complete") {
      const reversedHead = LinkedList.fromArray(step.listState);
      this.renderList(this.resultListContainer, reversedHead, "result");
    }
  }

  /**
   * 渲染处理过程中的链表
   * @param {Object} step - 动画步骤对象
   */
  renderProcessList(step) {
    // 这里简化处理，直接渲染当前状态
    const head = LinkedList.fromArray(step.listState);
    this.renderList(this.processListContainer, head, "process");

    // 高亮当前处理的节点
    if (step.current !== null && step.current !== undefined) {
      const nodes = this.processListContainer.querySelectorAll(".node-box");
      if (nodes[step.current]) {
        nodes[step.current].classList.add("current-node");
      }
    }

    if (step.prev !== null && step.prev !== undefined) {
      const nodes = this.processListContainer.querySelectorAll(".node-box");
      if (nodes[step.prev]) {
        nodes[step.prev].classList.add("prev-node");
      }
    }

    if (step.next !== null && step.next !== undefined) {
      const nodes = this.processListContainer.querySelectorAll(".node-box");
      if (nodes[step.next]) {
        nodes[step.next].classList.add("next-node");
      }
    }
  }

  /**
   * 更新指针位置
   * @param {Object} step - 动画步骤对象
   */
  updatePointers(step) {
    // 隐藏所有指针
    [this.prevPointer, this.currentPointer, this.nextPointer].forEach(
      (pointer) => {
        if (pointer) pointer.classList.remove("visible");
      }
    );

    // 根据步骤显示相应的指针
    if (step.prev !== null && step.prev !== undefined && this.prevPointer) {
      this.prevPointer.classList.add("visible");
    }

    if (
      step.current !== null &&
      step.current !== undefined &&
      this.currentPointer
    ) {
      this.currentPointer.classList.add("visible");
    }

    if (step.next !== null && step.next !== undefined && this.nextPointer) {
      this.nextPointer.classList.add("visible");
    }
  }

  /**
   * 暂停动画
   */
  pauseAnimation() {
    this.isPaused = true;
    if (this.animationTimer) {
      clearTimeout(this.animationTimer);
      this.animationTimer = null;
    }
    this.updateControlButtons();
  }

  /**
   * 继续动画
   */
  continueAnimation() {
    if (this.isPaused) {
      this.isPaused = false;
      this.executeAnimationStep();
    }
  }

  /**
   * 重置动画
   */
  resetAnimation() {
    this.isAnimating = false;
    this.isPaused = false;
    this.currentStep = 0;
    this.animationSteps = [];

    if (this.animationTimer) {
      clearTimeout(this.animationTimer);
      this.animationTimer = null;
    }

    // 清空显示容器
    this.processListContainer.innerHTML = "";
    this.resultListContainer.innerHTML = "";

    // 隐藏指针
    [this.prevPointer, this.currentPointer, this.nextPointer].forEach(
      (pointer) => {
        if (pointer) pointer.classList.remove("visible");
      }
    );

    // 重置步骤信息
    this.stepDescription.textContent = '点击"开始演示"开始可视化反转链表的过程';
    this.stepCounter.textContent = "0";
    this.totalSteps.textContent = "0";

    this.updateControlButtons();
  }

  /**
   * 单步执行
   */
  stepForward() {
    if (!this.originalList) {
      this.showMessage("请先创建链表", "error");
      return;
    }

    if (!this.isAnimating) {
      // 如果还没开始，先初始化
      const selectedAlgorithm = document.querySelector(
        'input[name="algorithm"]:checked'
      ).value;
      this.generateAnimationSteps(selectedAlgorithm);
      this.isAnimating = true;
    }

    if (this.currentStep < this.animationSteps.length) {
      const step = this.animationSteps[this.currentStep];
      this.renderAnimationStep(step);
      this.currentStep++;
      this.stepCounter.textContent = this.currentStep;

      if (this.currentStep >= this.animationSteps.length) {
        this.completeAnimation();
      }
    }

    this.updateControlButtons();
  }

  /**
   * 完成动画
   */
  completeAnimation() {
    this.isAnimating = false;
    this.isPaused = false;

    if (this.animationTimer) {
      clearTimeout(this.animationTimer);
      this.animationTimer = null;
    }

    this.stepDescription.textContent = "反转链表演示完成！";
    this.updateControlButtons();
    this.showMessage("动画演示完成", "success");
  }

  /**
   * 更新控制按钮状态
   */
  updateControlButtons() {
    if (!this.startBtn || !this.pauseBtn || !this.resetBtn || !this.stepBtn)
      return;

    if (this.isAnimating && !this.isPaused) {
      this.startBtn.disabled = true;
      this.pauseBtn.disabled = false;
      this.stepBtn.disabled = true;
    } else if (this.isAnimating && this.isPaused) {
      this.startBtn.textContent = "继续";
      this.startBtn.disabled = false;
      this.pauseBtn.disabled = true;
      this.stepBtn.disabled = false;
    } else {
      this.startBtn.textContent = "开始演示";
      this.startBtn.disabled = false;
      this.pauseBtn.disabled = true;
      this.stepBtn.disabled = false;
    }

    this.resetBtn.disabled = false;
  }

  /**
   * 切换代码标签页
   * @param {string} tabId - 标签页ID
   */
  switchTab(tabId) {
    // 移除所有活动状态
    this.tabBtns.forEach((btn) => btn.classList.remove("active"));
    this.codeContents.forEach((content) => content.classList.remove("active"));

    // 激活选中的标签页
    const activeBtn = document.querySelector(`[data-tab="${tabId}"]`);
    const activeContent = document.getElementById(tabId);

    if (activeBtn && activeContent) {
      activeBtn.classList.add("active");
      activeContent.classList.add("active");
    }
  }

  /**
   * 显示消息
   * @param {string} message - 消息内容
   * @param {string} type - 消息类型 ('success', 'error', 'info')
   */
  showMessage(message, type = "info") {
    // 创建消息元素
    const messageElement = document.createElement("div");
    messageElement.className = `message message-${type}`;
    messageElement.textContent = message;
    messageElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        `;

    // 设置背景色
    switch (type) {
      case "success":
        messageElement.style.background = "#28a745";
        break;
      case "error":
        messageElement.style.background = "#dc3545";
        break;
      default:
        messageElement.style.background = "#007bff";
    }

    // 添加到页面
    document.body.appendChild(messageElement);

    // 3秒后自动移除
    setTimeout(() => {
      if (messageElement.parentNode) {
        messageElement.style.animation = "slideOut 0.3s ease-in";
        setTimeout(() => {
          messageElement.remove();
        }, 300);
      }
    }, 3000);
  }
}

// 添加CSS动画
const style = document.createElement("style");
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// 页面加载完成后初始化动画控制器
document.addEventListener("DOMContentLoaded", () => {
  window.animator = new LinkedListAnimator();
});

/**
 * 工具函数：创建节点元素
 * @param {*} value - 节点值
 * @param {number} index - 节点索引
 * @param {string} type - 节点类型
 * @returns {HTMLElement} 节点元素
 */
function createNodeElement(value, index, type = "normal") {
  const nodeElement = document.createElement("div");
  nodeElement.className = "node";
  nodeElement.dataset.index = index;
  nodeElement.dataset.value = value;

  const nodeBox = document.createElement("div");
  nodeBox.className = `node-box ${type}`;
  nodeBox.textContent = value;

  // 添加节点动画效果
  nodeBox.style.animation = "nodeAppear 0.5s ease-out";

  nodeElement.appendChild(nodeBox);
  return nodeElement;
}

/**
 * 工具函数：创建箭头元素
 * @param {boolean} isReversed - 是否为反转状态
 * @returns {HTMLElement} 箭头元素
 */
function createArrowElement(isReversed = false) {
  const arrow = document.createElement("div");
  arrow.className = isReversed ? "arrow reversed" : "arrow";

  // 添加箭头动画效果
  if (isReversed) {
    arrow.style.animation = "arrowReverse 0.8s ease-in-out";
  }

  return arrow;
}

/**
 * 工具函数：高亮节点
 * @param {HTMLElement} nodeElement - 节点元素
 * @param {string} highlightType - 高亮类型
 * @param {number} duration - 高亮持续时间
 */
function highlightNode(
  nodeElement,
  highlightType = "highlighted",
  duration = 1000
) {
  if (!nodeElement) return;

  const nodeBox = nodeElement.querySelector(".node-box");
  if (nodeBox) {
    nodeBox.classList.add(highlightType);

    // 添加脉冲动画
    nodeBox.style.animation = "pulse 0.6s ease-in-out";

    setTimeout(() => {
      nodeBox.classList.remove(highlightType);
      nodeBox.style.animation = "";
    }, duration);
  }
}

/**
 * 工具函数：移动指针到指定位置
 * @param {HTMLElement} pointer - 指针元素
 * @param {HTMLElement} targetNode - 目标节点
 */
function movePointerToNode(pointer, targetNode) {
  if (!pointer || !targetNode) return;

  const rect = targetNode.getBoundingClientRect();
  const containerRect = targetNode
    .closest(".list-container")
    .getBoundingClientRect();

  const left = rect.left - containerRect.left + rect.width / 2 - 10;
  const top = rect.bottom - containerRect.top + 10;

  pointer.style.left = `${left}px`;
  pointer.style.top = `${top}px`;
  pointer.classList.add("visible");

  // 添加移动动画
  pointer.style.transition = "all 0.5s ease-in-out";
}

// 添加更多CSS动画样式
const additionalStyles = document.createElement("style");
additionalStyles.textContent = `
    @keyframes nodeAppear {
        0% {
            opacity: 0;
            transform: scale(0.5) rotate(180deg);
        }
        50% {
            opacity: 0.7;
            transform: scale(1.1) rotate(90deg);
        }
        100% {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }
    }

    @keyframes arrowReverse {
        0% {
            transform: scaleX(1);
            background-color: #2d3436;
        }
        50% {
            transform: scaleX(0.8);
            background-color: #e17055;
        }
        100% {
            transform: scaleX(1);
            background-color: #e17055;
        }
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 6px 25px rgba(232, 67, 147, 0.4);
        }
    }

    @keyframes pointerMove {
        0% {
            opacity: 0;
            transform: translateY(-10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .pointer.visible {
        animation: pointerMove 0.3s ease-out;
    }

    .node-box.processing {
        background: linear-gradient(135deg, #fdcb6e, #e17055);
        animation: processing 1.5s infinite;
    }

    @keyframes processing {
        0%, 100% {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        50% {
            box-shadow: 0 6px 25px rgba(253, 203, 110, 0.6);
        }
    }

    .empty-list {
        color: #6c757d;
        font-style: italic;
        padding: 20px;
        text-align: center;
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        background: #f8f9fa;
    }

    .null-indicator {
        color: #6c757d !important;
        font-weight: bold !important;
        padding: 10px !important;
        font-size: 14px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #dee2e6;
    }
`;
document.head.appendChild(additionalStyles);
