/* 反转链表可视化演示样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    margin-bottom: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #e0e0e0;
}

header h1 {
    font-size: 2.5em;
    color: #2c3e50;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

header p {
    font-size: 1.1em;
    color: #7f8c8d;
}

/* 输入控制区域 */
.input-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.input-group {
    margin-bottom: 15px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.input-group input[type="text"] {
    width: 300px;
    padding: 10px 15px;
    border: 2px solid #ced4da;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.input-group input[type="text"]:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.input-group button {
    margin-left: 10px;
    padding: 10px 20px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

.input-group button:hover {
    background: #0056b3;
}

.algorithm-selection {
    display: flex;
    align-items: center;
    gap: 15px;
}

.algorithm-selection label {
    margin: 0;
    font-weight: 500;
}

.algorithm-selection input[type="radio"] {
    margin-right: 5px;
}

/* 控制按钮 */
.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.controls button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 100px;
}

#startBtn {
    background: #28a745;
    color: white;
}

#startBtn:hover:not(:disabled) {
    background: #218838;
    transform: translateY(-2px);
}

#pauseBtn {
    background: #ffc107;
    color: #212529;
}

#pauseBtn:hover:not(:disabled) {
    background: #e0a800;
    transform: translateY(-2px);
}

#resetBtn {
    background: #dc3545;
    color: white;
}

#resetBtn:hover:not(:disabled) {
    background: #c82333;
    transform: translateY(-2px);
}

#stepBtn {
    background: #17a2b8;
    color: white;
}

#stepBtn:hover:not(:disabled) {
    background: #138496;
    transform: translateY(-2px);
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 速度控制 */
.speed-control {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.speed-control label {
    font-weight: 600;
    color: #495057;
}

.speed-control input[type="range"] {
    width: 200px;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.speed-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
}

.speed-control input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: none;
}

#speedValue {
    font-weight: bold;
    color: #007bff;
    min-width: 20px;
    text-align: center;
}

/* 可视化区域 */
.visualization-area {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid #e9ecef;
}

.list-container {
    margin-bottom: 40px;
    text-align: center;
}

.list-container h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3em;
    font-weight: 600;
}

.linked-list {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80px;
    flex-wrap: wrap;
    gap: 10px;
}

/* 链表节点样式 */
.node {
    display: flex;
    align-items: center;
    position: relative;
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}

.node-box {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    border: 3px solid #2d3436;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 18px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.node-box.highlighted {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(232, 67, 147, 0.4);
}

.node-box.prev-node {
    background: linear-gradient(135deg, #00b894, #00a085);
}

.node-box.current-node {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
}

.node-box.next-node {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
}

/* 箭头样式 */
.arrow {
    width: 40px;
    height: 4px;
    background: #2d3436;
    position: relative;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.arrow::after {
    content: '';
    position: absolute;
    right: -8px;
    top: -6px;
    width: 0;
    height: 0;
    border-left: 14px solid #2d3436;
    border-top: 7px solid transparent;
    border-bottom: 7px solid transparent;
}

.arrow.reversed {
    background: #e17055;
    animation: pulse 1s infinite;
}

.arrow.reversed::after {
    border-left-color: #e17055;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* 指针样式 */
.pointers {
    position: relative;
    height: 60px;
    margin-top: 20px;
}

.pointer {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0;
    transition: all 0.5s ease;
}

.pointer.visible {
    opacity: 1;
}

.pointer span {
    background: #2d3436;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 5px;
}

.prev-pointer span {
    background: #00b894;
}

.current-pointer span {
    background: #fdcb6e;
    color: #2d3436;
}

.next-pointer span {
    background: #a29bfe;
}

.pointer .arrow {
    width: 2px;
    height: 20px;
    background: #2d3436;
    margin: 0;
}

.pointer .arrow::after {
    right: -4px;
    top: 16px;
    border-left: 8px solid #2d3436;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
}

/* 步骤说明 */
.step-explanation {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
    border-left: 5px solid #007bff;
}

.step-explanation h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.step-text {
    font-size: 16px;
    line-height: 1.6;
    color: #495057;
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.step-counter {
    margin-top: 10px;
    font-weight: 600;
    color: #007bff;
}

/* 复杂度信息 */
.complexity-info {
    background: white;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.complexity-info h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.3em;
}

.complexity-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.complexity-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.complexity-item h4 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.1em;
    text-align: center;
}

.complexity-item p {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* 代码展示区域 */
.code-section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.code-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.3em;
}

.code-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.tab-btn {
    padding: 12px 24px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: #007bff;
    border-bottom-color: #007bff;
}

.tab-btn:hover {
    color: #007bff;
}

.code-content {
    display: none;
}

.code-content.active {
    display: block;
}

.code-content pre {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow-x: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.code-content code {
    color: #2d3436;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }
    
    .complexity-grid {
        grid-template-columns: 1fr;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .input-group input[type="text"] {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .input-group button {
        margin-left: 0;
    }
    
    .linked-list {
        flex-direction: column;
        gap: 20px;
    }
    
    .node {
        flex-direction: column;
    }
    
    .arrow {
        width: 4px;
        height: 30px;
        transform: rotate(90deg);
    }
}
