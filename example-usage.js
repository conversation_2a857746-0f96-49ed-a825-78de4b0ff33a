/**
 * 反转链表使用示例
 * 演示如何使用反转链表的各种功能
 */

// 引入链表类
const { ListNode, LinkedList } = require('./reverse-linked-list.js');

console.log('🔗 反转链表使用示例\n');
console.log('='.repeat(50));

// 示例1: 基本使用
console.log('\n📝 示例1: 基本使用');
console.log('-'.repeat(30));

// 创建链表
const originalArray = [1, 2, 3, 4, 5];
console.log(`原始数组: [${originalArray.join(', ')}]`);

const head = LinkedList.fromArray(originalArray);
console.log(`创建链表: ${LinkedList.printList(head)}`);

// 迭代方法反转
const reversedHead1 = LinkedList.reverseIterative(head);
console.log(`迭代反转: ${LinkedList.printList(reversedHead1)}`);

// 重新创建链表用于递归测试
const head2 = LinkedList.fromArray(originalArray);
const reversedHead2 = LinkedList.reverseRecursive(head2);
console.log(`递归反转: ${LinkedList.printList(reversedHead2)}`);

// 示例2: 处理特殊情况
console.log('\n📝 示例2: 特殊情况处理');
console.log('-'.repeat(30));

// 空链表
console.log('空链表测试:');
const emptyHead = LinkedList.fromArray([]);
const reversedEmpty = LinkedList.reverseIterative(emptyHead);
console.log(`  原始: ${LinkedList.printList(emptyHead)}`);
console.log(`  反转: ${LinkedList.printList(reversedEmpty)}`);

// 单个节点
console.log('\n单个节点测试:');
const singleHead = LinkedList.fromArray([42]);
const reversedSingle = LinkedList.reverseIterative(singleHead);
console.log(`  原始: ${LinkedList.printList(singleHead)}`);
console.log(`  反转: ${LinkedList.printList(reversedSingle)}`);

// 两个节点
console.log('\n两个节点测试:');
const twoNodesHead = LinkedList.fromArray([1, 2]);
const reversedTwo = LinkedList.reverseIterative(twoNodesHead);
console.log(`  原始: ${LinkedList.printList(twoNodesHead)}`);
console.log(`  反转: ${LinkedList.printList(reversedTwo)}`);

// 示例3: 不同数据类型
console.log('\n📝 示例3: 不同数据类型');
console.log('-'.repeat(30));

// 字符串链表
console.log('字符串链表:');
const stringArray = ['hello', 'world', 'javascript'];
const stringHead = LinkedList.fromArray(stringArray);
const reversedString = LinkedList.reverseIterative(stringHead);
console.log(`  原始: ${LinkedList.printList(stringHead)}`);
console.log(`  反转: ${LinkedList.printList(reversedString)}`);

// 混合数据类型
console.log('\n混合数据类型:');
const mixedArray = [1, 'hello', 3.14, true, null];
const mixedHead = LinkedList.fromArray(mixedArray);
const reversedMixed = LinkedList.reverseIterative(mixedHead);
console.log(`  原始: ${LinkedList.printList(mixedHead)}`);
console.log(`  反转: ${LinkedList.printList(reversedMixed)}`);

// 示例4: 性能测试
console.log('\n📝 示例4: 性能对比');
console.log('-'.repeat(30));

function performanceTest(size) {
    console.log(`\n测试链表大小: ${size}`);
    
    // 创建测试数据
    const testArray = Array.from({ length: size }, (_, i) => i + 1);
    
    // 测试迭代方法
    const iterativeHead = LinkedList.fromArray([...testArray]);
    const iterativeStart = process.hrtime.bigint();
    LinkedList.reverseIterative(iterativeHead);
    const iterativeEnd = process.hrtime.bigint();
    const iterativeTime = Number(iterativeEnd - iterativeStart) / 1000000; // 转换为毫秒
    
    console.log(`  迭代方法: ${iterativeTime.toFixed(3)}ms`);
    
    // 测试递归方法（小心栈溢出）
    if (size <= 1000) {
        try {
            const recursiveHead = LinkedList.fromArray([...testArray]);
            const recursiveStart = process.hrtime.bigint();
            LinkedList.reverseRecursive(recursiveHead);
            const recursiveEnd = process.hrtime.bigint();
            const recursiveTime = Number(recursiveEnd - recursiveStart) / 1000000;
            
            console.log(`  递归方法: ${recursiveTime.toFixed(3)}ms`);
            console.log(`  性能比: ${(recursiveTime / iterativeTime).toFixed(2)}x`);
        } catch (error) {
            console.log(`  递归方法: 错误 - ${error.message}`);
        }
    } else {
        console.log(`  递归方法: 跳过（数据量过大）`);
    }
}

// 运行性能测试
performanceTest(100);
performanceTest(1000);
performanceTest(10000);

// 示例5: 手动创建链表
console.log('\n📝 示例5: 手动创建链表');
console.log('-'.repeat(30));

// 手动创建链表节点
const node1 = new ListNode(1);
const node2 = new ListNode(2);
const node3 = new ListNode(3);

// 连接节点
node1.next = node2;
node2.next = node3;

console.log(`手动创建: ${LinkedList.printList(node1)}`);

// 反转手动创建的链表
const reversedManual = LinkedList.reverseIterative(node1);
console.log(`反转结果: ${LinkedList.printList(reversedManual)}`);

// 示例6: 链表转换
console.log('\n📝 示例6: 链表与数组转换');
console.log('-'.repeat(30));

const testArray = [10, 20, 30, 40, 50];
console.log(`原始数组: [${testArray.join(', ')}]`);

// 数组转链表
const listFromArray = LinkedList.fromArray(testArray);
console.log(`转为链表: ${LinkedList.printList(listFromArray)}`);

// 链表转数组
const arrayFromList = LinkedList.toArray(listFromArray);
console.log(`转回数组: [${arrayFromList.join(', ')}]`);

// 验证转换正确性
const isEqual = testArray.every((val, index) => val === arrayFromList[index]);
console.log(`转换正确: ${isEqual ? '✅' : '❌'}`);

// 示例7: 错误处理
console.log('\n📝 示例7: 错误处理');
console.log('-'.repeat(30));

try {
    // 测试null输入
    console.log('测试null输入:');
    const nullResult = LinkedList.reverseIterative(null);
    console.log(`  结果: ${nullResult === null ? 'null (正确)' : '错误'}`);
    
    // 测试undefined输入
    console.log('\n测试undefined输入:');
    const undefinedResult = LinkedList.reverseIterative(undefined);
    console.log(`  结果: ${undefinedResult === undefined ? 'undefined (正确)' : '错误'}`);
    
} catch (error) {
    console.log(`错误处理测试失败: ${error.message}`);
}

console.log('\n' + '='.repeat(50));
console.log('🎉 所有示例运行完成！');
console.log('\n💡 提示:');
console.log('- 打开 reverse-linked-list-demo.html 查看可视化演示');
console.log('- 运行 node test-reverse-linked-list.js 执行完整测试');
console.log('- 查看 README-reverse-linked-list.md 了解详细文档');
console.log('='.repeat(50));
