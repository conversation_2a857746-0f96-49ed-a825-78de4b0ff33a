/**
 * 反转链表测试文件
 * 用于验证链表反转算法的正确性
 */

// 引入链表类（如果在Node.js环境中）
let ListNode, LinkedList;
if (typeof require !== "undefined") {
  try {
    const linkedListModule = require("./reverse-linked-list.js");
    ListNode = linkedListModule.ListNode;
    LinkedList = linkedListModule.LinkedList;
  } catch (error) {
    console.error("无法导入链表模块:", error.message);
    process.exit(1);
  }
}

/**
 * 测试用例类
 */
class LinkedListTester {
  constructor() {
    this.testCases = [];
    this.passedTests = 0;
    this.totalTests = 0;
  }

  /**
   * 添加测试用例
   * @param {string} name - 测试用例名称
   * @param {Array} input - 输入数组
   * @param {Array} expected - 期望输出数组
   */
  addTestCase(name, input, expected) {
    this.testCases.push({ name, input, expected });
  }

  /**
   * 运行所有测试用例
   */
  runAllTests() {
    console.log("🧪 开始运行反转链表测试用例...\n");
    console.log("=".repeat(60));

    this.totalTests = this.testCases.length * 2; // 每个用例测试两种方法
    this.passedTests = 0;

    this.testCases.forEach((testCase, index) => {
      console.log(`\n📋 测试用例 ${index + 1}: ${testCase.name}`);
      console.log(`输入: [${testCase.input.join(", ")}]`);
      console.log(`期望: [${testCase.expected.join(", ")}]`);
      console.log("-".repeat(40));

      // 测试迭代方法
      this.runSingleTest(testCase, "iterative");

      // 测试递归方法
      this.runSingleTest(testCase, "recursive");
    });

    this.printSummary();
  }

  /**
   * 运行单个测试用例
   * @param {Object} testCase - 测试用例对象
   * @param {string} method - 测试方法 ('iterative' 或 'recursive')
   */
  runSingleTest(testCase, method) {
    try {
      // 创建链表
      const head = LinkedList.fromArray(testCase.input);

      // 执行反转
      let result;
      const startTime = performance.now();

      if (method === "iterative") {
        result = LinkedList.reverseIterative(head);
      } else {
        result = LinkedList.reverseRecursive(head);
      }

      const endTime = performance.now();
      const executionTime = (endTime - startTime).toFixed(3);

      // 转换结果为数组
      const resultArray = LinkedList.toArray(result);

      // 验证结果
      const isCorrect = this.arraysEqual(resultArray, testCase.expected);

      if (isCorrect) {
        console.log(`✅ ${method}方法: 通过 (${executionTime}ms)`);
        console.log(`   结果: [${resultArray.join(", ")}]`);
        this.passedTests++;
      } else {
        console.log(`❌ ${method}方法: 失败`);
        console.log(`   期望: [${testCase.expected.join(", ")}]`);
        console.log(`   实际: [${resultArray.join(", ")}]`);
      }
    } catch (error) {
      console.log(`💥 ${method}方法: 异常 - ${error.message}`);
    }
  }

  /**
   * 比较两个数组是否相等
   * @param {Array} arr1 - 数组1
   * @param {Array} arr2 - 数组2
   * @returns {boolean} 是否相等
   */
  arraysEqual(arr1, arr2) {
    if (arr1.length !== arr2.length) return false;
    return arr1.every((val, index) => val === arr2[index]);
  }

  /**
   * 打印测试总结
   */
  printSummary() {
    console.log("\n" + "=".repeat(60));
    console.log("📊 测试总结");
    console.log("=".repeat(60));
    console.log(`总测试数: ${this.totalTests}`);
    console.log(`通过测试: ${this.passedTests}`);
    console.log(`失败测试: ${this.totalTests - this.passedTests}`);
    console.log(
      `通过率: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`
    );

    if (this.passedTests === this.totalTests) {
      console.log("🎉 所有测试通过！");
    } else {
      console.log("⚠️  存在失败的测试用例，请检查实现");
    }
    console.log("=".repeat(60));
  }

  /**
   * 性能测试
   * @param {number} size - 链表大小
   */
  performanceTest(size = 10000) {
    console.log(`\n⚡ 性能测试 - 链表大小: ${size}`);
    console.log("-".repeat(40));

    // 创建大型测试数据
    const largeArray = Array.from({ length: size }, (_, i) => i + 1);
    const head1 = LinkedList.fromArray([...largeArray]);
    const head2 = LinkedList.fromArray([...largeArray]);

    // 测试迭代方法性能
    const iterativeStart = performance.now();
    LinkedList.reverseIterative(head1);
    const iterativeEnd = performance.now();
    const iterativeTime = iterativeEnd - iterativeStart;

    // 测试递归方法性能（小心栈溢出）
    let recursiveTime = "N/A";
    if (size <= 1000) {
      // 递归方法在大数据时可能栈溢出
      try {
        const recursiveStart = performance.now();
        LinkedList.reverseRecursive(head2);
        const recursiveEnd = performance.now();
        recursiveTime = (recursiveEnd - recursiveStart).toFixed(3) + "ms";
      } catch (error) {
        recursiveTime = `错误: ${error.message}`;
      }
    } else {
      recursiveTime = "跳过 (数据量过大，可能栈溢出)";
    }

    console.log(`迭代方法: ${iterativeTime.toFixed(3)}ms`);
    console.log(`递归方法: ${recursiveTime}`);

    // 内存使用分析
    console.log(`\n💾 内存复杂度分析:`);
    console.log(`迭代方法: O(1) - 只使用固定的额外空间`);
    console.log(`递归方法: O(n) - 递归调用栈深度为n`);
  }
}

/**
 * 运行测试
 */
function runTests() {
  const tester = new LinkedListTester();

  // 添加基本测试用例
  tester.addTestCase("正常链表", [1, 2, 3, 4, 5], [5, 4, 3, 2, 1]);
  tester.addTestCase("单个节点", [42], [42]);
  tester.addTestCase("两个节点", [1, 2], [2, 1]);
  tester.addTestCase("空链表", [], []);
  tester.addTestCase("重复元素", [1, 1, 2, 2], [2, 2, 1, 1]);
  tester.addTestCase("负数", [-1, -2, -3], [-3, -2, -1]);
  tester.addTestCase("混合数据", [0, -1, 5, -3, 2], [2, -3, 5, -1, 0]);

  // 运行所有测试
  tester.runAllTests();

  // 性能测试
  tester.performanceTest(1000);
  tester.performanceTest(10000);
}

/**
 * 边界条件测试
 */
function boundaryTests() {
  console.log("\n🔍 边界条件测试");
  console.log("=".repeat(40));

  // 测试null输入
  console.log("测试null输入:");
  try {
    const result1 = LinkedList.reverseIterative(null);
    const result2 = LinkedList.reverseRecursive(null);
    console.log(`✅ 迭代方法处理null: ${result1 === null ? "正确" : "错误"}`);
    console.log(`✅ 递归方法处理null: ${result2 === null ? "正确" : "错误"}`);
  } catch (error) {
    console.log(`❌ 处理null时出错: ${error.message}`);
  }

  // 测试大数值
  console.log("\n测试大数值:");
  try {
    const bigNumbers = [Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER, 0];
    const head = LinkedList.fromArray(bigNumbers);
    const result = LinkedList.reverseIterative(head);
    const resultArray = LinkedList.toArray(result);
    const expected = [0, Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER];

    const isCorrect = resultArray.every(
      (val, index) => val === expected[index]
    );
    console.log(`✅ 大数值处理: ${isCorrect ? "正确" : "错误"}`);
  } catch (error) {
    console.log(`❌ 大数值处理出错: ${error.message}`);
  }

  // 测试字符串数据
  console.log("\n测试字符串数据:");
  try {
    const strings = ["hello", "world", "test"];
    const head = LinkedList.fromArray(strings);
    const result = LinkedList.reverseIterative(head);
    const resultArray = LinkedList.toArray(result);
    const expected = ["test", "world", "hello"];

    const isCorrect = resultArray.every(
      (val, index) => val === expected[index]
    );
    console.log(`✅ 字符串处理: ${isCorrect ? "正确" : "错误"}`);
  } catch (error) {
    console.log(`❌ 字符串处理出错: ${error.message}`);
  }
}

// 如果在浏览器环境中，添加到window对象
if (typeof window !== "undefined") {
  window.runLinkedListTests = runTests;
  window.runBoundaryTests = boundaryTests;
  window.LinkedListTester = LinkedListTester;
}

// 如果在Node.js环境中，直接运行测试
if (typeof module !== "undefined" && require.main === module) {
  runTests();
  boundaryTests();
}

// 导出测试函数
if (typeof module !== "undefined") {
  module.exports = {
    runTests,
    boundaryTests,
    LinkedListTester,
  };
}
